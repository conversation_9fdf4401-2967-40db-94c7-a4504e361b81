import{S,i as A,s as K,X as L,a0 as x,a as g,D as v,E as M,a1 as y,a2 as h,e as d,F as N,g as V,u,t as $,h as f,G as X,a3 as j,I as k,j as B,J as D,V as H,c as E,q as O,r as P,K as F,L as G,M as I}from"./SpinnerAugment-CL9SZpf8.js";const Q=o=>({}),q=o=>({});function w(o){let l,c;const i=o[8].icon,a=D(i,o,o[9],q);return{c(){l=v("div"),a&&a.c(),E(l,"class","c-callout-icon svelte-16iebz8")},m(e,n){d(e,l,n),a&&a.m(l,null),c=!0},p(e,n){a&&a.p&&(!c||512&n)&&F(a,i,e,e[9],c?I(i,e[9],n,Q):G(e[9]),q)},i(e){c||(u(a,e),c=!0)},o(e){$(a,e),c=!1},d(e){e&&f(l),a&&a.d(e)}}}function R(o){let l,c,i,a=o[7].icon&&w(o);const e=o[8].default,n=D(e,o,o[9],null);return{c(){a&&a.c(),l=H(),c=v("div"),n&&n.c(),E(c,"class","c-callout-body")},m(t,s){a&&a.m(t,s),d(t,l,s),d(t,c,s),n&&n.m(c,null),i=!0},p(t,s){t[7].icon?a?(a.p(t,s),128&s&&u(a,1)):(a=w(t),a.c(),u(a,1),a.m(l.parentNode,l)):a&&(O(),$(a,1,1,()=>{a=null}),P()),n&&n.p&&(!i||512&s)&&F(n,e,t,t[9],i?I(e,t[9],s,null):G(t[9]),null)},i(t){i||(u(a),u(n,t),i=!0)},o(t){$(a),$(n,t),i=!1},d(t){t&&(f(l),f(c)),a&&a.d(t),n&&n.d(t)}}}function T(o){let l,c,i,a;c=new L({props:{size:o[6],$$slots:{default:[R]},$$scope:{ctx:o}}});let e=[x(o[0]),{class:i=`c-callout c-callout--${o[0]} c-callout--${o[1]} c-callout--size-${o[2]} ${o[5]}`},o[4]],n={};for(let t=0;t<e.length;t+=1)n=g(n,e[t]);return{c(){l=v("div"),M(c.$$.fragment),y(l,n),h(l,"c-callout--highContrast",o[3]),h(l,"svelte-16iebz8",!0)},m(t,s){d(t,l,s),N(c,l,null),a=!0},p(t,[s]){const p={};640&s&&(p.$$scope={dirty:s,ctx:t}),c.$set(p),y(l,n=V(e,[1&s&&x(t[0]),(!a||39&s&&i!==(i=`c-callout c-callout--${t[0]} c-callout--${t[1]} c-callout--size-${t[2]} ${t[5]}`))&&{class:i},16&s&&t[4]])),h(l,"c-callout--highContrast",t[3]),h(l,"svelte-16iebz8",!0)},i(t){a||(u(c.$$.fragment,t),a=!0)},o(t){$(c.$$.fragment,t),a=!1},d(t){t&&f(l),X(c)}}}function U(o,l,c){let i,a;const e=["color","variant","size","highContrast"];let n=j(l,e),{$$slots:t={},$$scope:s}=l;const p=k(t);let{color:z="info"}=l,{variant:C="soft"}=l,{size:m=2}=l,{highContrast:b=!1}=l;const J=m;return o.$$set=r=>{l=g(g({},l),B(r)),c(10,n=j(l,e)),"color"in r&&c(0,z=r.color),"variant"in r&&c(1,C=r.variant),"size"in r&&c(2,m=r.size),"highContrast"in r&&c(3,b=r.highContrast),"$$scope"in r&&c(9,s=r.$$scope)},o.$$.update=()=>{c(5,{class:i,...a}=n,i,(c(4,a),c(10,n)))},[z,C,m,b,a,i,J,p,t,s]}class Y extends S{constructor(l){super(),A(this,l,U,T,K,{color:0,variant:1,size:2,highContrast:3})}}export{Y as C};
