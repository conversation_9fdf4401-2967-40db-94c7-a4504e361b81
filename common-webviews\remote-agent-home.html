<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Agents</title>
    <script nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <script type="module" crossorigin src="./assets/remote-agent-home-eZzTIVQH.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-CL9SZpf8.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-SyQ8NwYv.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-APxr5XPC.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-BIfzX7Ug.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-C4xMcLhX.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-CtwQrvzD.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-SEbJxN6J.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-DDm27S8B.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/utils-0kgBFNBQ.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-D2Ut0gK2.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/StatusIndicator-Ck3wVb83.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-bwPj7Y67.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-C-hloZHD.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/terminal-D0NX0vvY.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/exclamation-triangle-5FhabZKw.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/augment-logo-CDzRYJ1a.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-BrD4yHQ_.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-B7KScrDw.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-vCST1yxq.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/StatusIndicator-D-yOSWp9.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BUhwqDnM.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-bpLK60vI.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/remote-agent-home-CuWF5Lfd.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
