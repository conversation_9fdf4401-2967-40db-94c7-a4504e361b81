import{_ as n,j as r,k as c,l as x}from"./AugmentMessage-DSvQSfka.js";var $=n((t,e)=>{let i;return e==="sandbox"&&(i=r("#i"+t)),r(e==="sandbox"?i.nodes()[0].contentDocument.body:"body").select(`[id="${t}"]`)},"getDiagramElement"),m=n((t,e,i,o)=>{t.attr("class",i);const{width:a,height:s,x:h,y:g}=w(t,e);c(t,s,a,o);const d=l(h,g,a,s,e);t.attr("viewBox",d),x.debug(`viewBox configured: ${d} with padding: ${e}`)},"setupViewPortForSVG"),w=n((t,e)=>{var o;const i=((o=t.node())==null?void 0:o.getBBox())||{width:0,height:0,x:0,y:0};return{width:i.width+2*e,height:i.height+2*e,x:i.x,y:i.y}},"calculateDimensionsWithPadding"),l=n((t,e,i,o,a)=>`${t-a} ${e-a} ${i} ${o}`,"createViewBox");export{$ as g,m as s};
