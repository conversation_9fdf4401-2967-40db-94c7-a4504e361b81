var Mt=Object.defineProperty;var Pt=(t,e,n)=>e in t?Mt(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var qe=(t,e,n)=>Pt(t,typeof e!="symbol"?e+"":e,n);import{S as re,i as ae,s as se,D as k,Y as E,V as R,E as j,c as y,e as D,f as w,F as I,Z as B,u as N,q as X,t as _,r as J,h as T,G as F,a8 as ge,n as G,ae as pt,a2 as L,a5 as _e,J as Dt,K as Tt,L as Ct,M as qt,X as St,Q as te,a6 as Rt,a7 as zt,N as Pe,af as $t,T as De}from"./SpinnerAugment-CL9SZpf8.js";import"./design-system-init-SyQ8NwYv.js";import"./design-system-init-BIfzX7Ug.js";import{e as Q,h as oe,W as Z,B as At,I as Se,u as bt,o as wt}from"./IconButtonAugment-C4xMcLhX.js";import{C as Ot}from"./CopyButton-BWQVMcne.js";import{B as Me}from"./ButtonAugment-iwbEjzvh.js";import{c as Wt,S as be,M as Et}from"./index-iuo-Ho0S.js";import{e as jt,R as ce}from"./toggleHighContrast-Th-X2FgN.js";import{C as Re}from"./next-edit-types-904A5ehg.js";import{T as It}from"./TextAreaAugment-CiMTZgUO.js";import{C as Ft}from"./CardAugment-bwPj7Y67.js";import{o as we}from"./keypress-DD1aQVr0.js";import{M as vt}from"./MaterialIcon-Bh8QWD0w.js";import"./copy-BFy87Ryv.js";import"./index-BskWw2a8.js";import"./preload-helper-Dv6uf1Os.js";import"./BaseTextInput-BAWt2_LS.js";function V(t){const e=Object.prototype.toString.call(t);return t instanceof Date||typeof t=="object"&&e==="[object Date]"?new t.constructor(+t):typeof t=="number"||e==="[object Number]"||typeof t=="string"||e==="[object String]"?new Date(t):new Date(NaN)}function ie(t,e){return t instanceof Date?new t.constructor(e):new Date(e)}const yt=6048e5,Yt=864e5,ze=6e4,Ae=36e5;let Ht={};function ye(){return Ht}function he(t,e){var c,s,l,u;const n=ye(),o=(e==null?void 0:e.weekStartsOn)??((s=(c=e==null?void 0:e.locale)==null?void 0:c.options)==null?void 0:s.weekStartsOn)??n.weekStartsOn??((u=(l=n.locale)==null?void 0:l.options)==null?void 0:u.weekStartsOn)??0,i=V(t),r=i.getDay(),a=(r<o?7:0)+r-o;return i.setDate(i.getDate()-a),i.setHours(0,0,0,0),i}function ve(t){return he(t,{weekStartsOn:1})}function xt(t){const e=V(t),n=e.getFullYear(),o=ie(t,0);o.setFullYear(n+1,0,4),o.setHours(0,0,0,0);const i=ve(o),r=ie(t,0);r.setFullYear(n,0,4),r.setHours(0,0,0,0);const a=ve(r);return e.getTime()>=i.getTime()?n+1:e.getTime()>=a.getTime()?n:n-1}function Oe(t){const e=V(t);return e.setHours(0,0,0,0),e}function We(t){const e=V(t),n=new Date(Date.UTC(e.getFullYear(),e.getMonth(),e.getDate(),e.getHours(),e.getMinutes(),e.getSeconds(),e.getMilliseconds()));return n.setUTCFullYear(e.getFullYear()),+t-+n}function Lt(t){if(e=t,!(e instanceof Date||typeof e=="object"&&Object.prototype.toString.call(e)==="[object Date]"||typeof t=="number"))return!1;var e;const n=V(t);return!isNaN(Number(n))}const Bt={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function xe(t){return(e={})=>{const n=e.width?String(e.width):t.defaultWidth;return t.formats[n]||t.formats[t.defaultWidth]}}const Qt={date:xe({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:xe({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:xe({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},Ut={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function le(t){return(e,n)=>{let o;if((n!=null&&n.context?String(n.context):"standalone")==="formatting"&&t.formattingValues){const i=t.defaultFormattingWidth||t.defaultWidth,r=n!=null&&n.width?String(n.width):i;o=t.formattingValues[r]||t.formattingValues[i]}else{const i=t.defaultWidth,r=n!=null&&n.width?String(n.width):t.defaultWidth;o=t.values[r]||t.values[i]}return o[t.argumentCallback?t.argumentCallback(e):e]}}function ue(t){return(e,n={})=>{const o=n.width,i=o&&t.matchPatterns[o]||t.matchPatterns[t.defaultMatchWidth],r=e.match(i);if(!r)return null;const a=r[0],c=o&&t.parsePatterns[o]||t.parsePatterns[t.defaultParseWidth],s=Array.isArray(c)?function(u,d){for(let m=0;m<u.length;m++)if(d(u[m]))return m}(c,u=>u.test(a)):function(u,d){for(const m in u)if(Object.prototype.hasOwnProperty.call(u,m)&&d(u[m]))return m}(c,u=>u.test(a));let l;return l=t.valueCallback?t.valueCallback(s):s,l=n.valueCallback?n.valueCallback(l):l,{value:l,rest:e.slice(a.length)}}}var de;const Gt={code:"en-US",formatDistance:(t,e,n)=>{let o;const i=Bt[t];return o=typeof i=="string"?i:e===1?i.one:i.other.replace("{{count}}",e.toString()),n!=null&&n.addSuffix?n.comparison&&n.comparison>0?"in "+o:o+" ago":o},formatLong:Qt,formatRelative:(t,e,n,o)=>Ut[t],localize:{ordinalNumber:(t,e)=>{const n=Number(t),o=n%100;if(o>20||o<10)switch(o%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:le({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:le({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:t=>t-1}),month:le({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:le({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:le({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(de={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:t=>parseInt(t,10)},(t,e={})=>{const n=t.match(de.matchPattern);if(!n)return null;const o=n[0],i=t.match(de.parsePattern);if(!i)return null;let r=de.valueCallback?de.valueCallback(i[0]):i[0];return r=e.valueCallback?e.valueCallback(r):r,{value:r,rest:t.slice(o.length)}}),era:ue({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:ue({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:t=>t+1}),month:ue({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:ue({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:ue({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}};function Xt(t){const e=V(t);return function(o,i){const r=Oe(o),a=Oe(i),c=+r-We(r),s=+a-We(a);return Math.round((c-s)/Yt)}(e,function(o){const i=V(o),r=ie(o,0);return r.setFullYear(i.getFullYear(),0,1),r.setHours(0,0,0,0),r}(e))+1}function Jt(t){const e=V(t),n=+ve(e)-+function(o){const i=xt(o),r=ie(o,0);return r.setFullYear(i,0,4),r.setHours(0,0,0,0),ve(r)}(e);return Math.round(n/yt)+1}function kt(t,e){var u,d,m,f;const n=V(t),o=n.getFullYear(),i=ye(),r=(e==null?void 0:e.firstWeekContainsDate)??((d=(u=e==null?void 0:e.locale)==null?void 0:u.options)==null?void 0:d.firstWeekContainsDate)??i.firstWeekContainsDate??((f=(m=i.locale)==null?void 0:m.options)==null?void 0:f.firstWeekContainsDate)??1,a=ie(t,0);a.setFullYear(o+1,0,r),a.setHours(0,0,0,0);const c=he(a,e),s=ie(t,0);s.setFullYear(o,0,r),s.setHours(0,0,0,0);const l=he(s,e);return n.getTime()>=c.getTime()?o+1:n.getTime()>=l.getTime()?o:o-1}function Zt(t,e){const n=V(t),o=+he(n,e)-+function(i,r){var u,d,m,f;const a=ye(),c=(r==null?void 0:r.firstWeekContainsDate)??((d=(u=r==null?void 0:r.locale)==null?void 0:u.options)==null?void 0:d.firstWeekContainsDate)??a.firstWeekContainsDate??((f=(m=a.locale)==null?void 0:m.options)==null?void 0:f.firstWeekContainsDate)??1,s=kt(i,r),l=ie(i,0);return l.setFullYear(s,0,c),l.setHours(0,0,0,0),he(l,r)}(n,e);return Math.round(o/yt)+1}function A(t,e){return(t<0?"-":"")+Math.abs(t).toString().padStart(e,"0")}const ee={y(t,e){const n=t.getFullYear(),o=n>0?n:1-n;return A(e==="yy"?o%100:o,e.length)},M(t,e){const n=t.getMonth();return e==="M"?String(n+1):A(n+1,2)},d:(t,e)=>A(t.getDate(),e.length),a(t,e){const n=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.toUpperCase();case"aaa":return n;case"aaaaa":return n[0];default:return n==="am"?"a.m.":"p.m."}},h:(t,e)=>A(t.getHours()%12||12,e.length),H:(t,e)=>A(t.getHours(),e.length),m:(t,e)=>A(t.getMinutes(),e.length),s:(t,e)=>A(t.getSeconds(),e.length),S(t,e){const n=e.length,o=t.getMilliseconds();return A(Math.trunc(o*Math.pow(10,n-3)),e.length)}},Vt="midnight",Kt="noon",en="morning",tn="afternoon",nn="evening",on="night",Ee={G:function(t,e,n){const o=t.getFullYear()>0?1:0;switch(e){case"G":case"GG":case"GGG":return n.era(o,{width:"abbreviated"});case"GGGGG":return n.era(o,{width:"narrow"});default:return n.era(o,{width:"wide"})}},y:function(t,e,n){if(e==="yo"){const o=t.getFullYear(),i=o>0?o:1-o;return n.ordinalNumber(i,{unit:"year"})}return ee.y(t,e)},Y:function(t,e,n,o){const i=kt(t,o),r=i>0?i:1-i;return e==="YY"?A(r%100,2):e==="Yo"?n.ordinalNumber(r,{unit:"year"}):A(r,e.length)},R:function(t,e){return A(xt(t),e.length)},u:function(t,e){return A(t.getFullYear(),e.length)},Q:function(t,e,n){const o=Math.ceil((t.getMonth()+1)/3);switch(e){case"Q":return String(o);case"QQ":return A(o,2);case"Qo":return n.ordinalNumber(o,{unit:"quarter"});case"QQQ":return n.quarter(o,{width:"abbreviated",context:"formatting"});case"QQQQQ":return n.quarter(o,{width:"narrow",context:"formatting"});default:return n.quarter(o,{width:"wide",context:"formatting"})}},q:function(t,e,n){const o=Math.ceil((t.getMonth()+1)/3);switch(e){case"q":return String(o);case"qq":return A(o,2);case"qo":return n.ordinalNumber(o,{unit:"quarter"});case"qqq":return n.quarter(o,{width:"abbreviated",context:"standalone"});case"qqqqq":return n.quarter(o,{width:"narrow",context:"standalone"});default:return n.quarter(o,{width:"wide",context:"standalone"})}},M:function(t,e,n){const o=t.getMonth();switch(e){case"M":case"MM":return ee.M(t,e);case"Mo":return n.ordinalNumber(o+1,{unit:"month"});case"MMM":return n.month(o,{width:"abbreviated",context:"formatting"});case"MMMMM":return n.month(o,{width:"narrow",context:"formatting"});default:return n.month(o,{width:"wide",context:"formatting"})}},L:function(t,e,n){const o=t.getMonth();switch(e){case"L":return String(o+1);case"LL":return A(o+1,2);case"Lo":return n.ordinalNumber(o+1,{unit:"month"});case"LLL":return n.month(o,{width:"abbreviated",context:"standalone"});case"LLLLL":return n.month(o,{width:"narrow",context:"standalone"});default:return n.month(o,{width:"wide",context:"standalone"})}},w:function(t,e,n,o){const i=Zt(t,o);return e==="wo"?n.ordinalNumber(i,{unit:"week"}):A(i,e.length)},I:function(t,e,n){const o=Jt(t);return e==="Io"?n.ordinalNumber(o,{unit:"week"}):A(o,e.length)},d:function(t,e,n){return e==="do"?n.ordinalNumber(t.getDate(),{unit:"date"}):ee.d(t,e)},D:function(t,e,n){const o=Xt(t);return e==="Do"?n.ordinalNumber(o,{unit:"dayOfYear"}):A(o,e.length)},E:function(t,e,n){const o=t.getDay();switch(e){case"E":case"EE":case"EEE":return n.day(o,{width:"abbreviated",context:"formatting"});case"EEEEE":return n.day(o,{width:"narrow",context:"formatting"});case"EEEEEE":return n.day(o,{width:"short",context:"formatting"});default:return n.day(o,{width:"wide",context:"formatting"})}},e:function(t,e,n,o){const i=t.getDay(),r=(i-o.weekStartsOn+8)%7||7;switch(e){case"e":return String(r);case"ee":return A(r,2);case"eo":return n.ordinalNumber(r,{unit:"day"});case"eee":return n.day(i,{width:"abbreviated",context:"formatting"});case"eeeee":return n.day(i,{width:"narrow",context:"formatting"});case"eeeeee":return n.day(i,{width:"short",context:"formatting"});default:return n.day(i,{width:"wide",context:"formatting"})}},c:function(t,e,n,o){const i=t.getDay(),r=(i-o.weekStartsOn+8)%7||7;switch(e){case"c":return String(r);case"cc":return A(r,e.length);case"co":return n.ordinalNumber(r,{unit:"day"});case"ccc":return n.day(i,{width:"abbreviated",context:"standalone"});case"ccccc":return n.day(i,{width:"narrow",context:"standalone"});case"cccccc":return n.day(i,{width:"short",context:"standalone"});default:return n.day(i,{width:"wide",context:"standalone"})}},i:function(t,e,n){const o=t.getDay(),i=o===0?7:o;switch(e){case"i":return String(i);case"ii":return A(i,e.length);case"io":return n.ordinalNumber(i,{unit:"day"});case"iii":return n.day(o,{width:"abbreviated",context:"formatting"});case"iiiii":return n.day(o,{width:"narrow",context:"formatting"});case"iiiiii":return n.day(o,{width:"short",context:"formatting"});default:return n.day(o,{width:"wide",context:"formatting"})}},a:function(t,e,n){const o=t.getHours()/12>=1?"pm":"am";switch(e){case"a":case"aa":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"});case"aaa":return n.dayPeriod(o,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return n.dayPeriod(o,{width:"narrow",context:"formatting"});default:return n.dayPeriod(o,{width:"wide",context:"formatting"})}},b:function(t,e,n){const o=t.getHours();let i;switch(i=o===12?Kt:o===0?Vt:o/12>=1?"pm":"am",e){case"b":case"bb":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"bbb":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return n.dayPeriod(i,{width:"narrow",context:"formatting"});default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},B:function(t,e,n){const o=t.getHours();let i;switch(i=o>=17?nn:o>=12?tn:o>=4?en:on,e){case"B":case"BB":case"BBB":return n.dayPeriod(i,{width:"abbreviated",context:"formatting"});case"BBBBB":return n.dayPeriod(i,{width:"narrow",context:"formatting"});default:return n.dayPeriod(i,{width:"wide",context:"formatting"})}},h:function(t,e,n){if(e==="ho"){let o=t.getHours()%12;return o===0&&(o=12),n.ordinalNumber(o,{unit:"hour"})}return ee.h(t,e)},H:function(t,e,n){return e==="Ho"?n.ordinalNumber(t.getHours(),{unit:"hour"}):ee.H(t,e)},K:function(t,e,n){const o=t.getHours()%12;return e==="Ko"?n.ordinalNumber(o,{unit:"hour"}):A(o,e.length)},k:function(t,e,n){let o=t.getHours();return o===0&&(o=24),e==="ko"?n.ordinalNumber(o,{unit:"hour"}):A(o,e.length)},m:function(t,e,n){return e==="mo"?n.ordinalNumber(t.getMinutes(),{unit:"minute"}):ee.m(t,e)},s:function(t,e,n){return e==="so"?n.ordinalNumber(t.getSeconds(),{unit:"second"}):ee.s(t,e)},S:function(t,e){return ee.S(t,e)},X:function(t,e,n){const o=t.getTimezoneOffset();if(o===0)return"Z";switch(e){case"X":return Ie(o);case"XXXX":case"XX":return ne(o);default:return ne(o,":")}},x:function(t,e,n){const o=t.getTimezoneOffset();switch(e){case"x":return Ie(o);case"xxxx":case"xx":return ne(o);default:return ne(o,":")}},O:function(t,e,n){const o=t.getTimezoneOffset();switch(e){case"O":case"OO":case"OOO":return"GMT"+je(o,":");default:return"GMT"+ne(o,":")}},z:function(t,e,n){const o=t.getTimezoneOffset();switch(e){case"z":case"zz":case"zzz":return"GMT"+je(o,":");default:return"GMT"+ne(o,":")}},t:function(t,e,n){return A(Math.trunc(t.getTime()/1e3),e.length)},T:function(t,e,n){return A(t.getTime(),e.length)}};function je(t,e=""){const n=t>0?"-":"+",o=Math.abs(t),i=Math.trunc(o/60),r=o%60;return r===0?n+String(i):n+String(i)+e+A(r,2)}function Ie(t,e){return t%60==0?(t>0?"-":"+")+A(Math.abs(t)/60,2):ne(t,e)}function ne(t,e=""){const n=t>0?"-":"+",o=Math.abs(t);return n+A(Math.trunc(o/60),2)+e+A(o%60,2)}const Fe=(t,e)=>{switch(t){case"P":return e.date({width:"short"});case"PP":return e.date({width:"medium"});case"PPP":return e.date({width:"long"});default:return e.date({width:"full"})}},Ye=(t,e)=>{switch(t){case"p":return e.time({width:"short"});case"pp":return e.time({width:"medium"});case"ppp":return e.time({width:"long"});default:return e.time({width:"full"})}},rn={p:Ye,P:(t,e)=>{const n=t.match(/(P+)(p+)?/)||[],o=n[1],i=n[2];if(!i)return Fe(t,e);let r;switch(o){case"P":r=e.dateTime({width:"short"});break;case"PP":r=e.dateTime({width:"medium"});break;case"PPP":r=e.dateTime({width:"long"});break;default:r=e.dateTime({width:"full"})}return r.replace("{{date}}",Fe(o,e)).replace("{{time}}",Ye(i,e))}},an=/^D+$/,sn=/^Y+$/,cn=["D","DD","YY","YYYY"],ln=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,un=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,dn=/^'([^]*?)'?$/,mn=/''/g,fn=/[a-zA-Z]/;function He(t,e,n){var u,d,m,f;const o=ye(),i=o.locale??Gt,r=o.firstWeekContainsDate??((d=(u=o.locale)==null?void 0:u.options)==null?void 0:d.firstWeekContainsDate)??1,a=o.weekStartsOn??((f=(m=o.locale)==null?void 0:m.options)==null?void 0:f.weekStartsOn)??0,c=V(t);if(!Lt(c))throw new RangeError("Invalid time value");let s=e.match(un).map(h=>{const v=h[0];return v==="p"||v==="P"?(0,rn[v])(h,i.formatLong):h}).join("").match(ln).map(h=>{if(h==="''")return{isToken:!1,value:"'"};const v=h[0];if(v==="'")return{isToken:!1,value:gn(h)};if(Ee[v])return{isToken:!0,value:h};if(v.match(fn))throw new RangeError("Format string contains an unescaped latin alphabet character `"+v+"`");return{isToken:!1,value:h}});i.localize.preprocessor&&(s=i.localize.preprocessor(c,s));const l={firstWeekContainsDate:r,weekStartsOn:a,locale:i};return s.map(h=>{if(!h.isToken)return h.value;const v=h.value;return(function(g){return sn.test(g)}(v)||function(g){return an.test(g)}(v))&&function(g,p,M){const $=function(C,z,S){const H=C[0]==="Y"?"years":"days of the month";return`Use \`${C.toLowerCase()}\` instead of \`${C}\` (in \`${z}\`) for formatting ${H} to the input \`${S}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}(g,p,M);if(console.warn($),cn.includes(g))throw new RangeError($)}(v,e,String(t)),(0,Ee[v[0]])(c,v,i.localize,l)}).join("")}function gn(t){const e=t.match(dn);return e?e[1].replace(mn,"'"):t}function ke(t,e){const n=function(c){const s={},l=c.split(pe.dateTimeDelimiter);let u;if(l.length>2)return s;if(/:/.test(l[0])?u=l[0]:(s.date=l[0],u=l[1],pe.timeZoneDelimiter.test(s.date)&&(s.date=c.split(pe.timeZoneDelimiter)[0],u=c.substr(s.date.length,c.length))),u){const d=pe.timezone.exec(u);d?(s.time=u.replace(d[1],""),s.timezone=d[1]):s.time=u}return s}(t);let o;if(n.date){const c=function(s,l){const u=new RegExp("^(?:(\\d{4}|[+-]\\d{"+(4+l)+"})|(\\d{2}|[+-]\\d{"+(2+l)+"})$)"),d=s.match(u);if(!d)return{year:NaN,restDateString:""};const m=d[1]?parseInt(d[1]):null,f=d[2]?parseInt(d[2]):null;return{year:f===null?m:100*f,restDateString:s.slice((d[1]||d[2]).length)}}(n.date,2);o=function(s,l){if(l===null)return new Date(NaN);const u=s.match(hn);if(!u)return new Date(NaN);const d=!!u[4],m=me(u[1]),f=me(u[2])-1,h=me(u[3]),v=me(u[4]),g=me(u[5])-1;if(d)return function(p,M,$){return M>=1&&M<=53&&$>=0&&$<=6}(0,v,g)?function(p,M,$){const C=new Date(0);C.setUTCFullYear(p,0,4);const z=C.getUTCDay()||7,S=7*(M-1)+$+1-z;return C.setUTCDate(C.getUTCDate()+S),C}(l,v,g):new Date(NaN);{const p=new Date(0);return function(M,$,C){return $>=0&&$<=11&&C>=1&&C<=(bn[$]||(Le(M)?29:28))}(l,f,h)&&function(M,$){return $>=1&&$<=(Le(M)?366:365)}(l,m)?(p.setUTCFullYear(l,f,Math.max(m,h)),p):new Date(NaN)}}(c.restDateString,c.year)}if(!o||isNaN(o.getTime()))return new Date(NaN);const i=o.getTime();let r,a=0;if(n.time&&(a=function(c){const s=c.match(pn);if(!s)return NaN;const l=Ne(s[1]),u=Ne(s[2]),d=Ne(s[3]);return function(m,f,h){return m===24?f===0&&h===0:h>=0&&h<60&&f>=0&&f<60&&m>=0&&m<25}(l,u,d)?l*Ae+u*ze+1e3*d:NaN}(n.time),isNaN(a)))return new Date(NaN);if(!n.timezone){const c=new Date(i+a),s=new Date(0);return s.setFullYear(c.getUTCFullYear(),c.getUTCMonth(),c.getUTCDate()),s.setHours(c.getUTCHours(),c.getUTCMinutes(),c.getUTCSeconds(),c.getUTCMilliseconds()),s}return r=function(c){if(c==="Z")return 0;const s=c.match($n);if(!s)return 0;const l=s[1]==="+"?-1:1,u=parseInt(s[2]),d=s[3]&&parseInt(s[3])||0;return function(m,f){return f>=0&&f<=59}(0,d)?l*(u*Ae+d*ze):NaN}(n.timezone),isNaN(r)?new Date(NaN):new Date(i+a+r)}const pe={dateTimeDelimiter:/[T ]/,timeZoneDelimiter:/[Z ]/i,timezone:/([Z+-].*)$/},hn=/^-?(?:(\d{3})|(\d{2})(?:-?(\d{2}))?|W(\d{2})(?:-?(\d{1}))?|)$/,pn=/^(\d{2}(?:[.,]\d*)?)(?::?(\d{2}(?:[.,]\d*)?))?(?::?(\d{2}(?:[.,]\d*)?))?$/,$n=/^([+-])(\d{2})(?::?(\d{2}))?$/;function me(t){return t?parseInt(t):1}function Ne(t){return t&&parseFloat(t.replace(",","."))||0}const bn=[31,null,31,30,31,30,31,31,30,31,30,31];function Le(t){return t%400==0||t%4==0&&t%100!=0}var U=(t=>(t[t.unset=0]="unset",t[t.positive=1]="positive",t[t.negative=2]="negative",t))(U||{});function Be(t,e,n){const o=t.slice();return o[7]=e[n],o}function wn(t){let e,n;return{c(){e=k("span"),n=E(t[1]),y(e,"slot","text"),y(e,"class","c-history-header--ellipsis svelte-8btr94")},m(o,i){D(o,e,i),w(e,n)},p(o,i){2&i&&B(n,o[1])},d(o){o&&T(e)}}}function Qe(t){let e,n,o,i,r;return i=new Me({props:{variant:"ghost-block",color:"neutral",size:1,title:"Click to open file",$$slots:{default:[vn]},$$scope:{ctx:t}}}),i.$on("click",t[5]),{c(){e=k("div"),n=k("span"),n.textContent="File:",o=R(),j(i.$$.fragment),y(n,"class","c-history-header__label svelte-8btr94"),y(e,"class","c-history-header__item svelte-8btr94")},m(a,c){D(a,e,c),w(e,n),w(e,o),I(i,e,null),r=!0},p(a,c){const s={};1028&c&&(s.$$scope={dirty:c,ctx:a}),i.$set(s)},i(a){r||(N(i.$$.fragment,a),r=!0)},o(a){_(i.$$.fragment,a),r=!1},d(a){a&&T(e),F(i)}}}function vn(t){let e,n,o;return{c(){e=k("span"),n=E("‎"),o=E(t[2]),y(e,"class","c-history-header--ellipsis-left svelte-8btr94")},m(i,r){D(i,e,r),w(e,n),w(e,o)},p(i,r){4&r&&B(o,i[2])},d(i){i&&T(e)}}}function Ue(t){let e,n,o,i,r;return{c(){e=k("div"),n=k("span"),n.textContent="Instruction:",o=R(),i=k("span"),r=E(t[3]),y(n,"class","c-history-header__label svelte-8btr94"),y(i,"class","c-history-header--ellipsis svelte-8btr94"),y(e,"class","c-history-header__item svelte-8btr94")},m(a,c){D(a,e,c),w(e,n),w(e,o),w(e,i),w(i,r)},p(a,c){8&c&&B(r,a[3])},d(a){a&&T(e)}}}function Ge(t){let e,n,o,i,r=t[7]+"";return{c(){e=k("div"),n=k("span"),o=E(r),i=R(),y(n,"class","c-history-header--ellipsis svelte-8btr94"),y(e,"class","c-history-header__item svelte-8btr94")},m(a,c){D(a,e,c),w(e,n),w(n,o),w(e,i)},p(a,c){16&c&&r!==(r=a[7]+"")&&B(o,r)},d(a){a&&T(e)}}}function yn(t){let e,n,o,i,r,a,c,s,l,u,d,m,f,h=He(t[0],"p 'on' P")+"";l=new Ot({props:{text:t[1],variant:"ghost-block",color:"neutral",size:1,tooltip:"Copy Request ID",$$slots:{text:[wn]},$$scope:{ctx:t}}});let v=t[2]&&Qe(t),g=t[3]&&Ue(t),p=Q(t[4]),M=[];for(let $=0;$<p.length;$+=1)M[$]=Ge(Be(t,p,$));return{c(){e=k("div"),n=k("div"),o=E(h),i=R(),r=k("div"),a=k("div"),c=k("span"),c.textContent="Request ID:",s=R(),j(l.$$.fragment),u=R(),v&&v.c(),d=R(),g&&g.c(),m=R();for(let $=0;$<M.length;$+=1)M[$].c();y(n,"class","c-history-header__timestamp svelte-8btr94"),y(c,"class","c-history-header__label svelte-8btr94"),y(a,"class","c-history-header__item svelte-8btr94"),y(r,"class","c-history-header__metadata svelte-8btr94"),y(e,"class","c-history-header svelte-8btr94")},m($,C){D($,e,C),w(e,n),w(n,o),w(e,i),w(e,r),w(r,a),w(a,c),w(a,s),I(l,a,null),w(r,u),v&&v.m(r,null),w(r,d),g&&g.m(r,null),w(r,m);for(let z=0;z<M.length;z+=1)M[z]&&M[z].m(r,null);f=!0},p($,[C]){(!f||1&C)&&h!==(h=He($[0],"p 'on' P")+"")&&B(o,h);const z={};if(2&C&&(z.text=$[1]),1026&C&&(z.$$scope={dirty:C,ctx:$}),l.$set(z),$[2]?v?(v.p($,C),4&C&&N(v,1)):(v=Qe($),v.c(),N(v,1),v.m(r,d)):v&&(X(),_(v,1,1,()=>{v=null}),J()),$[3]?g?g.p($,C):(g=Ue($),g.c(),g.m(r,m)):g&&(g.d(1),g=null),16&C){let S;for(p=Q($[4]),S=0;S<p.length;S+=1){const H=Be($,p,S);M[S]?M[S].p(H,C):(M[S]=Ge(H),M[S].c(),M[S].m(r,null))}for(;S<M.length;S+=1)M[S].d(1);M.length=p.length}},i($){f||(N(l.$$.fragment,$),N(v),f=!0)},o($){_(l.$$.fragment,$),_(v),f=!1},d($){$&&T(e),F(l),v&&v.d(),g&&g.d(),ge(M,$)}}}function xn(t,e,n){let{occuredAt:o}=e,{requestID:i}=e,{pathName:r=""}=e,{repoRoot:a}=e,{prompt:c=""}=e,{others:s=[]}=e;return t.$$set=l=>{"occuredAt"in l&&n(0,o=l.occuredAt),"requestID"in l&&n(1,i=l.requestID),"pathName"in l&&n(2,r=l.pathName),"repoRoot"in l&&n(6,a=l.repoRoot),"prompt"in l&&n(3,c=l.prompt),"others"in l&&n(4,s=l.others)},[o,i,r,c,s,function(){oe.postMessage({type:Z.openFile,data:{repoRoot:a,pathName:r}})},a]}class Nt extends re{constructor(e){super(),ae(this,e,xn,yn,se,{occuredAt:0,requestID:1,pathName:2,repoRoot:6,prompt:3,others:4})}}const Te={lineNumbers:"off",padding:{top:18,bottom:18}};function kn(t){let e,n,o,i;return o=new Wt({props:{options:Te,model:t[0],decorations:t[1]}}),{c(){e=k("div"),n=k("div"),j(o.$$.fragment),y(n,"class","c-completion-code-block__content svelte-krgqjl"),y(e,"class","c-completion-code-block svelte-krgqjl")},m(r,a){D(r,e,a),w(e,n),I(o,n,null),i=!0},p:G,i(r){i||(N(o.$$.fragment,r),i=!0)},o(r){_(o.$$.fragment,r),i=!1},d(r){r&&T(e),F(o)}}}const Xe=6;function Nn(t,e,n){let{prefix:o}=e,{suffix:i}=e,{completion:r}=e;const a=function(b){const P=b.split(`
`).slice(-Xe);for(let W=0;W<P.length;W++)if(P[W].trim().length>0)return P.slice(W).join(`
`);return""}(o),c=(s=i,!!(l=r.skippedSuffix)&&s.indexOf(l)===0);var s,l;const u=c?function(b,P){return P?b.indexOf(P)!==0?b:b.slice(P.length):b}(i,r.skippedSuffix):i,d=function(b){const P=b.split(`
`).slice(0,Xe);for(let W=P.length-1;W>=0;W--)if(P[W].trim().length>0)return P.slice(0,W+1).join(`
`);return""}(u),m=r.text,f=c?r.skippedSuffix:"",h=r.suffixReplacementText,v=a+m+f+h+d,g=jt.createModel(v,"plaintext"),p=g.getPositionAt(0),M=g.getPositionAt(a.length),$=g.getPositionAt(a.length),C=g.getPositionAt(a.length+m.length),z=g.getPositionAt(a.length+m.length),S=g.getPositionAt(a.length+m.length+f.length),H=g.getPositionAt(a.length+m.length+f.length),O=g.getPositionAt(a.length+m.length+f.length+h.length),x=g.getPositionAt(a.length+m.length+f.length+h.length),q=g.getPositionAt(v.length),Y=[{range:new ce(p.lineNumber,p.column,M.lineNumber,M.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new ce(x.lineNumber,x.column,q.lineNumber,q.column),options:{inlineClassName:"c-completion-code-block--dull"}},{range:new ce($.lineNumber,$.column,C.lineNumber,C.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new ce(H.lineNumber,H.column,O.lineNumber,O.column),options:{inlineClassName:"c-completion-code-block--addition"}},{range:new ce(z.lineNumber,z.column,S.lineNumber,S.column),options:{inlineClassName:"c-completion-code-block--strikethrough"}}];return pt(()=>{g==null||g.dispose()}),t.$$set=b=>{"prefix"in b&&n(2,o=b.prefix),"suffix"in b&&n(3,i=b.suffix),"completion"in b&&n(4,r=b.completion)},[g,Y,o,i,r]}class _n extends re{constructor(e){super(),ae(this,e,Nn,kn,se,{prefix:2,suffix:3,completion:4})}}const Je=["Thanks for the feedback!","Thanks for improving Augment!","Thanks for taking the time!","Thanks for helping Augment improve!","Thanks for helping us enhance Augment!","We value your input. Thanks for improving Augment!","Your insights are making a difference. Cheers!"],fe=new class{constructor(){qe(this,"_state");this._state=oe.getState()||{},this._state.feedback=this._state.feedback||{}}getFeedback(t){return this._state.feedback[t]?this._state.feedback[t]:{selectedRating:U.unset,feedbackNote:""}}setFeedback(t,e){this._state.feedback[t]=e,oe.setState(this._state)}cleanupFeedback(t){for(const e of Object.keys(this._state.feedback))t[e]||delete this._state.feedback[e];oe.setState(this._state)}};function Ze(t,e,n){const o=t.slice();return o[17]=e[n],o}const Mn=t=>({option:2&t,size:4&t}),Ve=t=>({option:$e(t[17]),size:t[2]});function Pn(t){let e,n=(typeof t[17]=="string"?t[17]:t[17].label)+"";return{c(){e=E(n)},m(o,i){D(o,e,i)},p(o,i){2&i&&n!==(n=(typeof o[17]=="string"?o[17]:o[17].label)+"")&&B(e,n)},d(o){o&&T(e)}}}function Dn(t){let e,n;const o=t[11]["option-button-contents"],i=Dt(o,t,t[15],Ve),r=i||function(a){let c,s;return c=new St({props:{size:a[2]===.5?1:a[2],$$slots:{default:[Pn]},$$scope:{ctx:a}}}),{c(){j(c.$$.fragment)},m(l,u){I(c,l,u),s=!0},p(l,u){const d={};4&u&&(d.size=l[2]===.5?1:l[2]),32770&u&&(d.$$scope={dirty:u,ctx:l}),c.$set(d)},i(l){s||(N(c.$$.fragment,l),s=!0)},o(l){_(c.$$.fragment,l),s=!1},d(l){F(c,l)}}}(t);return{c(){r&&r.c(),e=R()},m(a,c){r&&r.m(a,c),D(a,e,c),n=!0},p(a,c){i?i.p&&(!n||32774&c)&&Tt(i,o,a,a[15],n?qt(o,a[15],c,Mn):Ct(a[15]),Ve):r&&r.p&&(!n||6&c)&&r.p(a,n?c:-1)},i(a){n||(N(r,a),n=!0)},o(a){_(r,a),n=!1},d(a){a&&T(e),r&&r.d(a)}}}function Ke(t){let e,n;return e=new At({props:{size:t[2],disabled:t[3],variant:"ghost",color:"neutral",class:"c-toggle-button__button "+(t[17]===t[0]?"c-toggle-button__button--active":""),$$slots:{default:[Dn]},$$scope:{ctx:t}}}),e.$on("click",function(){return t[13](t[17])}),{c(){j(e.$$.fragment)},m(o,i){I(e,o,i),n=!0},p(o,i){t=o;const r={};4&i&&(r.size=t[2]),8&i&&(r.disabled=t[3]),3&i&&(r.class="c-toggle-button__button "+(t[17]===t[0]?"c-toggle-button__button--active":"")),32774&i&&(r.$$scope={dirty:i,ctx:t}),e.$set(r)},i(o){n||(N(e.$$.fragment,o),n=!0)},o(o){_(e.$$.fragment,o),n=!1},d(o){F(e,o)}}}function Tn(t){let e,n,o,i,r=Q(t[1]),a=[];for(let s=0;s<r.length;s+=1)a[s]=Ke(Ze(t,r,s));const c=s=>_(a[s],1,1,()=>{a[s]=null});return{c(){e=k("div"),n=k("div"),o=R();for(let s=0;s<a.length;s+=1)a[s].c();y(n,"class","background-slider"),y(e,"class","c-toggle-button svelte-tvrla5"),L(e,"c-toggle-button--disabled",t[3]),L(e,"c-toggle-button--size-0_5",t[2]===.5),L(e,"c-toggle-button--size-1",t[2]===1),L(e,"c-toggle-button--size-2",t[2]===2),L(e,"c-toggle-button--size-3",t[2]===3),L(e,"c-toggle-button--size-4",t[2]===4),L(e,"c-toggle-button--resizing",t[6])},m(s,l){D(s,e,l),w(e,n),t[12](n),w(e,o);for(let u=0;u<a.length;u+=1)a[u]&&a[u].m(e,null);t[14](e),i=!0},p(s,[l]){if(32911&l){let u;for(r=Q(s[1]),u=0;u<r.length;u+=1){const d=Ze(s,r,u);a[u]?(a[u].p(d,l),N(a[u],1)):(a[u]=Ke(d),a[u].c(),N(a[u],1),a[u].m(e,null))}for(X(),u=r.length;u<a.length;u+=1)c(u);J()}(!i||8&l)&&L(e,"c-toggle-button--disabled",s[3]),(!i||4&l)&&L(e,"c-toggle-button--size-0_5",s[2]===.5),(!i||4&l)&&L(e,"c-toggle-button--size-1",s[2]===1),(!i||4&l)&&L(e,"c-toggle-button--size-2",s[2]===2),(!i||4&l)&&L(e,"c-toggle-button--size-3",s[2]===3),(!i||4&l)&&L(e,"c-toggle-button--size-4",s[2]===4),(!i||64&l)&&L(e,"c-toggle-button--resizing",s[6])},i(s){if(!i){for(let l=0;l<r.length;l+=1)N(a[l]);i=!0}},o(s){a=a.filter(Boolean);for(let l=0;l<a.length;l+=1)_(a[l]);i=!1},d(s){s&&T(e),t[12](null),ge(a,s),t[14](null)}}}function $e(t){return typeof t=="string"?t:t==null?void 0:t.value}function Cn(t,e,n){let o,i,r,{$$slots:a={},$$scope:c}=e,{options:s}=e,{size:l=2}=e,{disabled:u=!1}=e,{onSelectOption:d}=e,{activeOption:m=$e(s[0])}=e;function f(p){!u&&d(p)&&n(0,m=p)}function h(){const p=o==null?void 0:o.querySelectorAll(".c-toggle-button__button");if(!p)return;const M=p[s.findIndex($=>$e($)===m)];if(o&&i&&M){const $=M.getBoundingClientRect(),C=o.getBoundingClientRect();n(5,i.style.left=$.left-C.left+"px",i),n(5,i.style.width=`${$.width}px`,i),n(5,i.style.height=`${$.height}px`,i)}}let v,g=!1;return pt(()=>{r==null||r.disconnect(),n(9,r=void 0),clearTimeout(v)}),t.$$set=p=>{"options"in p&&n(1,s=p.options),"size"in p&&n(2,l=p.size),"disabled"in p&&n(3,u=p.disabled),"onSelectOption"in p&&n(8,d=p.onSelectOption),"activeOption"in p&&n(0,m=p.activeOption),"$$scope"in p&&n(15,c=p.$$scope)},t.$$.update=()=>{1&t.$$.dirty&&m&&h(),1552&t.$$.dirty&&o&&!r&&(n(9,r=new ResizeObserver(()=>{n(6,g=!0),h(),clearTimeout(v),n(10,v=setTimeout(()=>{n(6,g=!1)},100))})),r.observe(o))},[m,s,l,u,o,i,g,f,d,r,v,a,function(p){_e[p?"unshift":"push"](()=>{i=p,n(5,i)})},p=>f($e(p)),function(p){_e[p?"unshift":"push"](()=>{o=p,n(4,o)})},c]}class _t extends re{constructor(e){super(),ae(this,e,Cn,Tn,se,{options:1,size:2,disabled:3,onSelectOption:8,activeOption:0})}}function et(t,e,n){const o=t.slice();return o[36]=e[n],o}function tt(t,e,n){const o=t.slice();return o[36]=e[n],o}function nt(t,e,n){const o=t.slice();return o[36]=e[n],o}function qn(t){let e,n,o,i,r,a=Q(t[9]),c=[];for(let d=0;d<a.length;d+=1)c[d]=ot(tt(t,a,d));const s=d=>_(c[d],1,1,()=>{c[d]=null});let l=Q(t[10]),u=[];for(let d=0;d<l.length;d+=1)u[d]=it(et(t,l,d));return{c(){e=k("section");for(let d=0;d<c.length;d+=1)c[d].c();n=R(),o=k("div"),o.textContent="Unchanged locations:",i=R();for(let d=0;d<u.length;d+=1)u[d].c();y(o,"class","c-unified-history-item__no-modifications svelte-179mxe5")},m(d,m){D(d,e,m);for(let f=0;f<c.length;f+=1)c[f]&&c[f].m(e,null);w(e,n),w(e,o),w(e,i);for(let f=0;f<u.length;f+=1)u[f]&&u[f].m(e,null);r=!0},p(d,m){if(2097920&m[0]){let f;for(a=Q(d[9]),f=0;f<a.length;f+=1){const h=tt(d,a,f);c[f]?(c[f].p(h,m),N(c[f],1)):(c[f]=ot(h),c[f].c(),N(c[f],1),c[f].m(e,n))}for(X(),f=a.length;f<c.length;f+=1)s(f);J()}if(2098432&m[0]){let f;for(l=Q(d[10]),f=0;f<l.length;f+=1){const h=et(d,l,f);u[f]?u[f].p(h,m):(u[f]=it(h),u[f].c(),u[f].m(e,null))}for(;f<u.length;f+=1)u[f].d(1);u.length=l.length}},i(d){if(!r){for(let m=0;m<a.length;m+=1)N(c[m]);r=!0}},o(d){c=c.filter(Boolean);for(let m=0;m<c.length;m+=1)_(c[m]);r=!1},d(d){d&&T(e),ge(c,d),ge(u,d)}}}function Sn(t){let e,n,o=Q(t[0].completions),i=[];for(let a=0;a<o.length;a+=1)i[a]=rt(nt(t,o,a));const r=a=>_(i[a],1,1,()=>{i[a]=null});return{c(){for(let a=0;a<i.length;a+=1)i[a].c();e=Pe()},m(a,c){for(let s=0;s<i.length;s+=1)i[s]&&i[s].m(a,c);D(a,e,c),n=!0},p(a,c){if(1&c[0]){let s;for(o=Q(a[0].completions),s=0;s<o.length;s+=1){const l=nt(a,o,s);i[s]?(i[s].p(l,c),N(i[s],1)):(i[s]=rt(l),i[s].c(),N(i[s],1),i[s].m(e.parentNode,e))}for(X(),s=o.length;s<i.length;s+=1)r(s);J()}},i(a){if(!n){for(let c=0;c<o.length;c+=1)N(i[c]);n=!0}},o(a){i=i.filter(Boolean);for(let c=0;c<i.length;c+=1)_(i[c]);n=!1},d(a){a&&T(e),ge(i,a)}}}function ot(t){let e,n,o,i,r,a,c,s,l,u,d,m,f,h,v,g,p,M,$,C,z,S=t[36].qualifiedPathName.relPath+"",H=t[36].lineRange.start+(t[36].lineRange.start<t[36].lineRange.stop?1:0)+"",O=t[36].lineRange.stop+"",x=t[36].result.changeDescription+"";function q(){return t[24](t[36])}function Y(){return t[25](t[36])}return g=new be({props:{text:t[36].result.existingCode,pathName:t[36].qualifiedPathName.relPath,options:{lineNumbers:"off"}}}),M=new be({props:{text:t[36].result.suggestedCode,pathName:t[36].qualifiedPathName.relPath,options:{lineNumbers:"off"}}}),{c(){e=k("div"),n=k("pre"),o=k("code"),i=k("span"),r=E(S),a=E(": "),c=E(H),s=E("-"),l=E(O),u=R(),d=k("div"),m=E(x),f=R(),h=k("section"),v=E(`original:
            `),j(g.$$.fragment),p=E(`
            modified:
            `),j(M.$$.fragment),y(i,"class","c-next-edit-addition svelte-179mxe5"),L(i,"c-next-edit-addition-clicked",t[8]===t[36]),y(n,"data-language","plaintext"),y(e,"class","c-completion-code-block"),y(e,"role","button"),y(e,"tabindex","0")},m(b,P){D(b,e,P),w(e,n),w(n,o),w(o,i),w(i,r),w(i,a),w(i,c),w(i,s),w(i,l),D(b,u,P),D(b,d,P),w(d,m),D(b,f,P),D(b,h,P),w(h,v),I(g,h,null),w(h,p),I(M,h,null),$=!0,C||(z=[te(e,"click",q),te(e,"keydown",function(){$t(we("Enter",Y))&&we("Enter",Y).apply(this,arguments)})],C=!0)},p(b,P){t=b,(!$||512&P[0])&&S!==(S=t[36].qualifiedPathName.relPath+"")&&B(r,S),(!$||512&P[0])&&H!==(H=t[36].lineRange.start+(t[36].lineRange.start<t[36].lineRange.stop?1:0)+"")&&B(c,H),(!$||512&P[0])&&O!==(O=t[36].lineRange.stop+"")&&B(l,O),(!$||768&P[0])&&L(i,"c-next-edit-addition-clicked",t[8]===t[36]),(!$||512&P[0])&&x!==(x=t[36].result.changeDescription+"")&&B(m,x);const W={};512&P[0]&&(W.text=t[36].result.existingCode),512&P[0]&&(W.pathName=t[36].qualifiedPathName.relPath),g.$set(W);const K={};512&P[0]&&(K.text=t[36].result.suggestedCode),512&P[0]&&(K.pathName=t[36].qualifiedPathName.relPath),M.$set(K)},i(b){$||(N(g.$$.fragment,b),N(M.$$.fragment,b),$=!0)},o(b){_(g.$$.fragment,b),_(M.$$.fragment,b),$=!1},d(b){b&&(T(e),T(u),T(d),T(f),T(h)),F(g),F(M),C=!1,De(z)}}}function it(t){let e,n,o,i,r,a,c,s,l,u,d,m,f=t[36].qualifiedPathName.relPath+"",h=t[36].lineRange.start+(t[36].lineRange.start<t[36].lineRange.stop?1:0)+"",v=t[36].lineRange.stop+"";function g(){return t[26](t[36])}function p(){return t[27](t[36])}return{c(){e=k("div"),n=k("pre"),o=k("code"),i=k("span"),r=E(f),a=E(": "),c=E(h),s=E("-"),l=E(v),u=R(),y(i,"class","c-next-edit-addition svelte-179mxe5"),L(i,"c-next-edit-addition-clicked",t[8]===t[36]),y(n,"data-language","plaintext"),y(n,"class","c-next-edit-addition svelte-179mxe5"),y(e,"class","c-completion-code-block"),y(e,"role","button"),y(e,"tabindex","0")},m(M,$){D(M,e,$),w(e,n),w(n,o),w(o,i),w(i,r),w(i,a),w(i,c),w(i,s),w(i,l),w(e,u),d||(m=[te(e,"click",g),te(e,"keydown",function(){$t(we("Enter",p))&&we("Enter",p).apply(this,arguments)})],d=!0)},p(M,$){t=M,1024&$[0]&&f!==(f=t[36].qualifiedPathName.relPath+"")&&B(r,f),1024&$[0]&&h!==(h=t[36].lineRange.start+(t[36].lineRange.start<t[36].lineRange.stop?1:0)+"")&&B(c,h),1024&$[0]&&v!==(v=t[36].lineRange.stop+"")&&B(l,v),1280&$[0]&&L(i,"c-next-edit-addition-clicked",t[8]===t[36])},d(M){M&&T(e),d=!1,De(m)}}}function rt(t){let e,n,o,i;return n=new _n({props:{completion:t[36],prefix:t[0].prefix,suffix:t[0].suffix}}),{c(){e=k("div"),j(n.$$.fragment),o=R(),y(e,"class","c-unified-history-item__code-block svelte-179mxe5")},m(r,a){D(r,e,a),I(n,e,null),w(e,o),i=!0},p(r,a){const c={};1&a[0]&&(c.completion=r[36]),1&a[0]&&(c.prefix=r[0].prefix),1&a[0]&&(c.suffix=r[0].suffix),n.$set(c)},i(r){i||(N(n.$$.fragment,r),i=!0)},o(r){_(n.$$.fragment,r),i=!1},d(r){r&&T(e),F(n)}}}function Rn(t){let e,n;return e=new vt({props:{iconName:"thumb_down",fill:t[3].selectedRating===U.negative}}),{c(){j(e.$$.fragment)},m(o,i){I(e,o,i),n=!0},p(o,i){const r={};8&i[0]&&(r.fill=o[3].selectedRating===U.negative),e.$set(r)},i(o){n||(N(e.$$.fragment,o),n=!0)},o(o){_(e.$$.fragment,o),n=!1},d(o){F(e,o)}}}function zn(t){let e,n;return e=new vt({props:{iconName:"thumb_up",fill:t[3].selectedRating===U.positive}}),{c(){j(e.$$.fragment)},m(o,i){I(e,o,i),n=!0},p(o,i){const r={};8&i[0]&&(r.fill=o[3].selectedRating===U.positive),e.$set(r)},i(o){n||(N(e.$$.fragment,o),n=!0)},o(o){_(e.$$.fragment,o),n=!1},d(o){F(e,o)}}}function at(t){let e,n,o,i,r,a,c,s,l,u;function d(f){t[30](f)}let m={rows:"4",placeholder:"Enter your feedback...",resize:"none"};return t[7]!==void 0&&(m.value=t[7]),o=new It({props:m}),_e.push(()=>Rt(o,"value",d)),c=new Me({props:{variant:"ghost",size:2,$$slots:{default:[An]},$$scope:{ctx:t}}}),c.$on("click",t[19]),l=new Me({props:{variant:"solid",size:2,disabled:t[7].trim().length===0,$$slots:{default:[On]},$$scope:{ctx:t}}}),l.$on("click",t[20]),{c(){e=k("div"),n=k("div"),j(o.$$.fragment),r=R(),a=k("div"),j(c.$$.fragment),s=R(),j(l.$$.fragment),y(n,"class","c-unified-history-item__feedback-content svelte-179mxe5"),y(a,"class","c-unified-history-item__feedback-actions svelte-179mxe5"),y(e,"class","c-unified-history-item__feedback-area svelte-179mxe5")},m(f,h){D(f,e,h),w(e,n),I(o,n,null),w(e,r),w(e,a),I(c,a,null),w(a,s),I(l,a,null),u=!0},p(f,h){const v={};!i&&128&h[0]&&(i=!0,v.value=f[7],zt(()=>i=!1)),o.$set(v);const g={};4096&h[1]&&(g.$$scope={dirty:h,ctx:f}),c.$set(g);const p={};128&h[0]&&(p.disabled=f[7].trim().length===0),4096&h[1]&&(p.$$scope={dirty:h,ctx:f}),l.$set(p)},i(f){u||(N(o.$$.fragment,f),N(c.$$.fragment,f),N(l.$$.fragment,f),u=!0)},o(f){_(o.$$.fragment,f),_(c.$$.fragment,f),_(l.$$.fragment,f),u=!1},d(f){f&&T(e),F(o),F(c),F(l)}}}function An(t){let e;return{c(){e=E("Cancel")},m(n,o){D(n,e,o)},d(n){n&&T(e)}}}function On(t){let e;return{c(){e=E("Share Feedback")},m(n,o){D(n,e,o)},d(n){n&&T(e)}}}function Wn(t){let e,n,o,i,r,a,c,s,l,u,d,m,f,h,v,g,p,M,$;n=new Nt({props:{occuredAt:t[15],requestID:t[11],pathName:t[14],repoRoot:t[13],others:t[1]?[`Request type: ${t[1].mode}/${t[1].scope}`]:void 0}});let C=t[16].length>1&&function(x){let q,Y,b;return Y=new _t({props:{options:x[16],onSelectOption:x[22],activeOption:x[2],size:1}}),{c(){q=k("div"),j(Y.$$.fragment),y(q,"class","c-unified-history-item__tabs svelte-179mxe5")},m(P,W){D(P,q,W),I(Y,q,null),b=!0},p(P,W){const K={};4&W[0]&&(K.activeOption=P[2]),Y.$set(K)},i(P){b||(N(Y.$$.fragment,P),b=!0)},o(P){_(Y.$$.fragment,P),b=!1},d(P){P&&T(q),F(Y)}}}(t);const z=[Sn,qn],S=[];function H(x,q){return x[12]==="completion"&&x[0]?0:x[12]==="nextEdit"&&x[1]?1:-1}~(a=H(t))&&(c=S[a]=z[a](t)),m=new Se({props:{variant:"ghost",color:t[3].selectedRating===U.negative?"error":"neutral",size:2,disabled:t[4],title:"Leave feedback about this completion",$$slots:{default:[Rn]},$$scope:{ctx:t}}}),m.$on("click",t[28]),h=new Se({props:{variant:"ghost",color:t[3].selectedRating===U.positive?"success":"neutral",size:2,disabled:t[4],title:"Leave feedback about this completion",$$slots:{default:[zn]},$$scope:{ctx:t}}}),h.$on("click",t[29]);let O=t[6]&&at(t);return{c(){e=k("div"),j(n.$$.fragment),o=R(),C&&C.c(),i=R(),r=k("div"),c&&c.c(),s=R(),l=k("div"),u=k("div"),d=k("div"),j(m.$$.fragment),f=R(),j(h.$$.fragment),v=R(),g=k("div"),p=E(t[5]),M=R(),O&&O.c(),y(e,"class","c-unified-history-item__header svelte-179mxe5"),y(r,"class","c-unified-history-item__content svelte-179mxe5"),y(d,"class","c-unified-history-item__rating-buttons svelte-179mxe5"),y(g,"class","c-unified-history-item__thankyou svelte-179mxe5"),y(u,"class","c-unified-history-item__ratings svelte-179mxe5"),y(l,"class","c-unified-history-item__footer svelte-179mxe5")},m(x,q){D(x,e,q),I(n,e,null),D(x,o,q),C&&C.m(x,q),D(x,i,q),D(x,r,q),~a&&S[a].m(r,null),D(x,s,q),D(x,l,q),w(l,u),w(u,d),I(m,d,null),w(d,f),I(h,d,null),w(u,v),w(u,g),w(g,p),w(l,M),O&&O.m(l,null),$=!0},p(x,q){const Y={};32768&q[0]&&(Y.occuredAt=x[15]),2048&q[0]&&(Y.requestID=x[11]),16384&q[0]&&(Y.pathName=x[14]),8192&q[0]&&(Y.repoRoot=x[13]),2&q[0]&&(Y.others=x[1]?[`Request type: ${x[1].mode}/${x[1].scope}`]:void 0),n.$set(Y),x[16].length>1&&C.p(x,q);let b=a;a=H(x),a===b?~a&&S[a].p(x,q):(c&&(X(),_(S[b],1,1,()=>{S[b]=null}),J()),~a?(c=S[a],c?c.p(x,q):(c=S[a]=z[a](x),c.c()),N(c,1),c.m(r,null)):c=null);const P={};8&q[0]&&(P.color=x[3].selectedRating===U.negative?"error":"neutral"),16&q[0]&&(P.disabled=x[4]),8&q[0]|4096&q[1]&&(P.$$scope={dirty:q,ctx:x}),m.$set(P);const W={};8&q[0]&&(W.color=x[3].selectedRating===U.positive?"success":"neutral"),16&q[0]&&(W.disabled=x[4]),8&q[0]|4096&q[1]&&(W.$$scope={dirty:q,ctx:x}),h.$set(W),(!$||32&q[0])&&B(p,x[5]),x[6]?O?(O.p(x,q),64&q[0]&&N(O,1)):(O=at(x),O.c(),N(O,1),O.m(l,null)):O&&(X(),_(O,1,1,()=>{O=null}),J())},i(x){$||(N(n.$$.fragment,x),N(C),N(c),N(m.$$.fragment,x),N(h.$$.fragment,x),N(O),$=!0)},o(x){_(n.$$.fragment,x),_(C),_(c),_(m.$$.fragment,x),_(h.$$.fragment,x),_(O),$=!1},d(x){x&&(T(e),T(o),T(i),T(r),T(s),T(l)),F(n),C&&C.d(x),~a&&S[a].d(),F(m),F(h),O&&O.d()}}}function En(t){let e,n,o,i;return e=new Ft({props:{size:2,variant:"surface",class:"c-unified-history-item "+(t[4]?"c-unified-history-item--sending-feedback":""),$$slots:{default:[Wn]},$$scope:{ctx:t}}}),{c(){j(e.$$.fragment)},m(r,a){I(e,r,a),n=!0,o||(i=te(window,"message",t[17]),o=!0)},p(r,a){const c={};16&a[0]&&(c.class="c-unified-history-item "+(r[4]?"c-unified-history-item--sending-feedback":"")),65535&a[0]|4096&a[1]&&(c.$$scope={dirty:a,ctx:r}),e.$set(c)},i(r){n||(N(e.$$.fragment,r),n=!0)},o(r){_(e.$$.fragment,r),n=!1},d(r){F(e,r),o=!1,i()}}}function jn(t,e,n){let o,i,r,a,c,{completion:s}=e,{nextEdit:l}=e,{demo:u=!1}=e,d=o==="completion"?"Completion":"Next Edit";const m=o==="completion"?["Completion"]:["Next Edit"];let f,h,v,g=fe.getFeedback(i),p=!1,M="",$=!1,C=null,z="",S=[],H=[];function O(){n(5,M=Je[Math.floor(Math.random()*Je.length)]),f&&clearTimeout(f),f=setTimeout(()=>{n(5,M="")},4e3)}function x(b){n(3,g.selectedRating=b,g),C=b,n(7,z=""),n(6,$=!0)}function q(){n(6,$=!1),C=null,n(7,z=""),n(3,g.selectedRating=U.unset,g)}function Y(b){oe.postMessage({type:Z.openFile,data:{repoRoot:b.qualifiedPathName.rootPath,pathName:b.result.path,range:b.lineRange,differentTab:!0}}),n(8,v=b)}return t.$$set=b=>{"completion"in b&&n(0,s=b.completion),"nextEdit"in b&&n(1,l=b.nextEdit),"demo"in b&&n(23,u=b.demo)},t.$$.update=()=>{var b;1&t.$$.dirty[0]&&n(12,o=s?"completion":"nextEdit"),3&t.$$.dirty[0]&&n(11,i=(s==null?void 0:s.requestId)||(l==null?void 0:l.requestId)||""),3&t.$$.dirty[0]&&n(15,r=(s==null?void 0:s.occuredAt)||(l==null?void 0:l.occurredAt)||new Date),1&t.$$.dirty[0]&&n(14,a=(s==null?void 0:s.pathName)||""),3&t.$$.dirty[0]&&n(13,c=(s==null?void 0:s.repoRoot)||((b=l==null?void 0:l.qualifiedPathName)==null?void 0:b.rootPath)||""),2&t.$$.dirty[0]&&l&&(n(9,S=l.suggestions.filter(P=>P.changeType!==Re.noop)),n(10,H=l.suggestions.filter(P=>P.changeType===Re.noop)))},[s,l,d,g,p,M,$,z,v,S,H,i,o,c,a,r,m,function(b){if(u)return;const P=b.data;switch(P.type){case Z.completionRatingDone:{const{requestId:W}=P.data;if(W!==i)return;n(4,p=!1),P.data.success||(n(3,g.selectedRating=h,g),fe.setFeedback(i,g));break}case Z.nextEditRatingDone:{const{requestId:W}=P.data;if(W!==i)return;n(4,p=!1),P.data.success||(n(3,g.selectedRating=h,g),fe.setFeedback(i,g));break}}},x,q,function(){C&&z.trim().length!==0&&(function(b,P){if(O(),h=g.selectedRating,b!==U.unset&&n(3,g.selectedRating=b,g),u)return;let W=P||g.feedbackNote;fe.setFeedback(i,g),n(4,p=!0);const K=o==="completion"?Z.completionRating:Z.nextEditRating;oe.postMessage({type:K,data:{requestId:i,rating:b,note:W.trim()}})}(C,z.trim()),q())},Y,function(b){return n(2,d=b),!0},u,b=>Y(b),b=>Y(b),b=>Y(b),b=>Y(b),()=>x(U.negative),()=>x(U.positive),function(b){z=b,n(7,z)}]}class Ce extends re{constructor(e){super(),ae(this,e,jn,En,se,{completion:0,nextEdit:1,demo:23},null,[-1,-1])}}function In(t){let e,n,o,i,r,a,c,s;return c=new Ce({props:{completion:t[0],demo:!0}}),{c(){e=k("div"),n=k("div"),n.innerHTML=`<h2>History.</h2> <p>As you use Augment, we&#39;ll display the most recent suggestions here so you can tell us about
      any particularly good, or bad, suggestions.</p> <p>Below is an example of the information and feedback form we&#39;ll display for each suggestion.</p>`,o=R(),i=k("div"),r=R(),a=k("div"),j(c.$$.fragment),y(n,"class","l-no-items__msg svelte-10bvc8"),y(i,"class","l-no-items__divider svelte-10bvc8"),y(a,"class","l-no-items__example svelte-10bvc8"),y(e,"class","l-no-items svelte-10bvc8")},m(l,u){D(l,e,u),w(e,n),w(e,o),w(e,i),w(e,r),w(e,a),I(c,a,null),s=!0},p:G,i(l){s||(N(c.$$.fragment,l),s=!0)},o(l){_(c.$$.fragment,l),s=!1},d(l){l&&T(e),F(c)}}}function Fn(t){return[{occuredAt:new Date,requestId:"12345678-1234-1234-1234-123456789123",repoRoot:"/home/<USER>/projects/example-project",pathName:"src/example.js",prefix:"co",completions:[{text:'nsole.log("Hello World.");',skippedSuffix:"",suffixReplacementText:""}],suffix:`

`}]}class Yn extends re{constructor(e){super(),ae(this,e,Fn,In,se,{})}}function Hn(t){let e,n,o;return{c(){e=k("div"),e.textContent="Click to view diff",y(e,"class","c-instruction-item__no-modifications svelte-15p7ohn"),y(e,"role","button"),y(e,"tabindex","0")},m(i,r){D(i,e,r),n||(o=[te(e,"keyup",t[4]),te(e,"click",t[4])],n=!0)},p:G,i:G,o:G,d(i){i&&T(e),n=!1,De(o)}}}function Ln(t){let e,n,o,i,r,a,c;o=new be({props:{options:Te,text:t[3],pathName:t[0].pathName}});const s=[Un,Qn],l=[];function u(d,m){return d[3]!==d[2]?0:1}return r=u(t),a=l[r]=s[r](t),{c(){e=k("section"),n=E(`original:
      `),j(o.$$.fragment),i=R(),a.c()},m(d,m){D(d,e,m),w(e,n),I(o,e,null),w(e,i),l[r].m(e,null),c=!0},p(d,m){const f={};8&m&&(f.text=d[3]),1&m&&(f.pathName=d[0].pathName),o.$set(f);let h=r;r=u(d),r===h?l[r].p(d,m):(X(),_(l[h],1,1,()=>{l[h]=null}),J(),a=l[r],a?a.p(d,m):(a=l[r]=s[r](d),a.c()),N(a,1),a.m(e,null))},i(d){c||(N(o.$$.fragment,d),N(a),c=!0)},o(d){_(o.$$.fragment,d),_(a),c=!1},d(d){d&&T(e),F(o),l[r].d()}}}function Bn(t){let e;return{c(){e=k("div"),e.textContent="No modification to original code",y(e,"class","c-instruction-item__no-modifications svelte-15p7ohn")},m(n,o){D(n,e,o)},p:G,i:G,o:G,d(n){n&&T(e)}}}function Qn(t){let e;return{c(){e=k("div"),e.textContent="No modification to original code",y(e,"class","c-instruction-item__no-modifications svelte-15p7ohn")},m(n,o){D(n,e,o)},p:G,i:G,o:G,d(n){n&&T(e)}}}function Un(t){let e,n,o;return n=new be({props:{options:Te,text:t[2],pathName:t[0].pathName}}),{c(){e=E(`modified:
        `),j(n.$$.fragment)},m(i,r){D(i,e,r),I(n,i,r),o=!0},p(i,r){const a={};4&r&&(a.text=i[2]),1&r&&(a.pathName=i[0].pathName),n.$set(a)},i(i){o||(N(n.$$.fragment,i),o=!0)},o(i){_(n.$$.fragment,i),o=!1},d(i){i&&T(e),F(n,i)}}}function Gn(t){let e,n,o,i,r,a;n=new Nt({props:{occuredAt:t[0].occuredAt,requestID:t[0].requestId,pathName:t[0].pathName,repoRoot:t[0].repoRoot,prompt:t[0].prompt}});const c=[Bn,Ln,Hn],s=[];function l(u,d){return u[0].selectedText===u[0].modifiedText?0:u[0].userRequested||u[1]===u[0].requestId?1:2}return i=l(t),r=s[i]=c[i](t),{c(){e=k("div"),j(n.$$.fragment),o=R(),r.c(),y(e,"class","c-instruction-item svelte-15p7ohn")},m(u,d){D(u,e,d),I(n,e,null),w(e,o),s[i].m(e,null),a=!0},p(u,[d]){const m={};1&d&&(m.occuredAt=u[0].occuredAt),1&d&&(m.requestID=u[0].requestId),1&d&&(m.pathName=u[0].pathName),1&d&&(m.repoRoot=u[0].repoRoot),1&d&&(m.prompt=u[0].prompt),n.$set(m);let f=i;i=l(u),i===f?s[i].p(u,d):(X(),_(s[f],1,1,()=>{s[f]=null}),J(),r=s[i],r?r.p(u,d):(r=s[i]=c[i](u),r.c()),N(r,1),r.m(e,null))},i(u){a||(N(n.$$.fragment,u),N(r),a=!0)},o(u){_(n.$$.fragment,u),_(r),a=!1},d(u){u&&T(e),F(n),s[i].d()}}}function st(t){const e=t.split(`
`);for(let n=e.length-1;n>=0;n--)if(e[n].trim().length>0)return e.slice(0,n+1).join(`
`);return""}function Xn(t,e,n){let o,i,r,{instruction:a}=e;return t.$$set=c=>{"instruction"in c&&n(0,a=c.instruction)},t.$$.update=()=>{1&t.$$.dirty&&n(3,o=st(a.selectedText)),1&t.$$.dirty&&n(2,i=st(a.modifiedText))},[a,r,i,o,function(){n(1,r=a.requestId)}]}class Jn extends re{constructor(e){super(),ae(this,e,Xn,Gn,se,{instruction:0})}}function ct(t,e,n){const o=t.slice();return o[15]=e[n],o[17]=n,o}function lt(t,e,n){const o=t.slice();return o[15]=e[n],o[17]=n,o}function Zn(t){let e,n,o,i,r,a,c;e=new _t({props:{options:t[3],onSelectOption:t[6],activeOption:t[1],size:2}});let s=t[4].length>0&&ut(t);const l=[eo,Kn],u=[];function d(m,f){return m[2].length===0?0:1}return r=d(t),a=u[r]=l[r](t),{c(){j(e.$$.fragment),n=R(),s&&s.c(),o=R(),i=k("div"),a.c(),y(i,"class","l-items-list__panel-content")},m(m,f){I(e,m,f),D(m,n,f),s&&s.m(m,f),D(m,o,f),D(m,i,f),u[r].m(i,null),c=!0},p(m,f){const h={};8&f&&(h.options=m[3]),2&f&&(h.activeOption=m[1]),e.$set(h),m[4].length>0?s?(s.p(m,f),16&f&&N(s,1)):(s=ut(m),s.c(),N(s,1),s.m(o.parentNode,o)):s&&(X(),_(s,1,1,()=>{s=null}),J());let v=r;r=d(m),r===v?u[r].p(m,f):(X(),_(u[v],1,1,()=>{u[v]=null}),J(),a=u[r],a?a.p(m,f):(a=u[r]=l[r](m),a.c()),N(a,1),a.m(i,null))},i(m){c||(N(e.$$.fragment,m),N(s),N(a),c=!0)},o(m){_(e.$$.fragment,m),_(s),_(a),c=!1},d(m){m&&(T(n),T(o),T(i)),F(e,m),s&&s.d(m),u[r].d()}}}function Vn(t){let e,n,o;return n=new Yn({}),{c(){e=k("div"),j(n.$$.fragment),y(e,"class","l-items-list__empty svelte-5e6wj2")},m(i,r){D(i,e,r),I(n,e,null),o=!0},p:G,i(i){o||(N(n.$$.fragment,i),o=!0)},o(i){_(n.$$.fragment,i),o=!1},d(i){i&&T(e),F(n)}}}function ut(t){let e,n,o,i,r,a=[],c=new Map,s=Q(t[4]);const l=u=>u[15].requestId;for(let u=0;u<s.length;u+=1){let d=lt(t,s,u),m=l(d);c.set(m,a[u]=mt(m,d))}return{c(){e=k("div"),n=k("h3"),n.textContent="Instructions",o=R(),i=k("div");for(let u=0;u<a.length;u+=1)a[u].c();y(n,"class","l-items-list__section-title svelte-5e6wj2"),y(i,"class","l-items-list__content svelte-5e6wj2"),y(e,"class","l-items-list__instructions-section svelte-5e6wj2")},m(u,d){D(u,e,d),w(e,n),w(e,o),w(e,i);for(let m=0;m<a.length;m+=1)a[m]&&a[m].m(i,null);r=!0},p(u,d){16&d&&(s=Q(u[4]),X(),a=bt(a,d,l,1,u,s,c,i,wt,mt,null,lt),J())},i(u){if(!r){for(let d=0;d<s.length;d+=1)N(a[d]);r=!0}},o(u){for(let d=0;d<a.length;d+=1)_(a[d]);r=!1},d(u){u&&T(e);for(let d=0;d<a.length;d+=1)a[d].d()}}}function dt(t){let e;return{c(){e=k("div"),y(e,"class","l-items-list__divider svelte-5e6wj2")},m(n,o){D(n,e,o)},d(n){n&&T(e)}}}function mt(t,e){let n,o,i,r,a;o=new Jn({props:{instruction:ht(e[15])}});let c=e[17]<e[4].length-1&&dt();return{key:t,first:null,c(){n=k("div"),j(o.$$.fragment),i=R(),c&&c.c(),r=Pe(),y(n,"class","l-items-list__item svelte-5e6wj2"),this.first=n},m(s,l){D(s,n,l),I(o,n,null),D(s,i,l),c&&c.m(s,l),D(s,r,l),a=!0},p(s,l){e=s;const u={};16&l&&(u.instruction=ht(e[15])),o.$set(u),e[17]<e[4].length-1?c||(c=dt(),c.c(),c.m(r.parentNode,r)):c&&(c.d(1),c=null)},i(s){a||(N(o.$$.fragment,s),a=!0)},o(s){_(o.$$.fragment,s),a=!1},d(s){s&&(T(n),T(i),T(r)),F(o),c&&c.d(s)}}}function Kn(t){let e,n,o=[],i=new Map,r=Q(t[2]);const a=c=>c[15].requestId;for(let c=0;c<r.length;c+=1){let s=ct(t,r,c),l=a(s);i.set(l,o[c]=gt(l,s))}return{c(){e=k("div");for(let c=0;c<o.length;c+=1)o[c].c();y(e,"class","l-items-list__content svelte-5e6wj2")},m(c,s){D(c,e,s);for(let l=0;l<o.length;l+=1)o[l]&&o[l].m(e,null);n=!0},p(c,s){4&s&&(r=Q(c[2]),X(),o=bt(o,s,a,1,c,r,i,e,wt,gt,null,ct),J())},i(c){if(!n){for(let s=0;s<r.length;s+=1)N(o[s]);n=!0}},o(c){for(let s=0;s<o.length;s+=1)_(o[s]);n=!1},d(c){c&&T(e);for(let s=0;s<o.length;s+=1)o[s].d()}}}function eo(t){let e,n,o,i,r,a=t[1].toLowerCase()+"";return{c(){e=k("div"),n=k("p"),o=E("No "),i=E(a),r=E(" found."),y(e,"class","l-items-list__empty-panel svelte-5e6wj2")},m(c,s){D(c,e,s),w(e,n),w(n,o),w(n,i),w(n,r)},p(c,s){2&s&&a!==(a=c[1].toLowerCase()+"")&&B(i,a)},i:G,o:G,d(c){c&&T(e)}}}function to(t){let e,n;return e=new Ce({props:{nextEdit:t[15].result}}),{c(){j(e.$$.fragment)},m(o,i){I(e,o,i),n=!0},p(o,i){const r={};4&i&&(r.nextEdit=o[15].result),e.$set(r)},i(o){n||(N(e.$$.fragment,o),n=!0)},o(o){_(e.$$.fragment,o),n=!1},d(o){F(e,o)}}}function no(t){let e,n;return e=new Ce({props:{completion:t[15]}}),{c(){j(e.$$.fragment)},m(o,i){I(e,o,i),n=!0},p(o,i){const r={};4&i&&(r.completion=o[15]),e.$set(r)},i(o){n||(N(e.$$.fragment,o),n=!0)},o(o){_(e.$$.fragment,o),n=!1},d(o){F(e,o)}}}function ft(t){let e;return{c(){e=k("div"),y(e,"class","l-items-list__divider svelte-5e6wj2")},m(n,o){D(n,e,o)},d(n){n&&T(e)}}}function gt(t,e){let n,o,i,r,a,c;const s=[no,to],l=[];function u(m,f){return"completions"in m[15]?0:"result"in m[15]?1:-1}~(o=u(e))&&(i=l[o]=s[o](e));let d=e[17]<e[2].length-1&&ft();return{key:t,first:null,c(){n=k("div"),i&&i.c(),r=R(),d&&d.c(),a=Pe(),y(n,"class","l-items-list__item svelte-5e6wj2"),this.first=n},m(m,f){D(m,n,f),~o&&l[o].m(n,null),D(m,r,f),d&&d.m(m,f),D(m,a,f),c=!0},p(m,f){let h=o;o=u(e=m),o===h?~o&&l[o].p(e,f):(i&&(X(),_(l[h],1,1,()=>{l[h]=null}),J()),~o?(i=l[o],i?i.p(e,f):(i=l[o]=s[o](e),i.c()),N(i,1),i.m(n,null)):i=null),e[17]<e[2].length-1?d||(d=ft(),d.c(),d.m(a.parentNode,a)):d&&(d.d(1),d=null)},i(m){c||(N(i),c=!0)},o(m){_(i),c=!1},d(m){m&&(T(n),T(r),T(a)),~o&&l[o].d(),d&&d.d(m)}}}function oo(t){let e,n,o,i;const r=[Vn,Zn],a=[];function c(s,l){return s[0].length?1:0}return n=c(t),o=a[n]=r[n](t),{c(){e=k("main"),o.c(),y(e,"class","l-items-list svelte-5e6wj2")},m(s,l){D(s,e,l),a[n].m(e,null),i=!0},p(s,l){let u=n;n=c(s),n===u?a[n].p(s,l):(X(),_(a[u],1,1,()=>{a[u]=null}),J(),o=a[n],o?o.p(s,l):(o=a[n]=r[n](s),o.c()),N(o,1),o.m(e,null))},i(s){i||(N(o),i=!0)},o(s){_(o),i=!1},d(s){s&&T(e),a[n].d()}}}function io(t){let e,n,o,i;return e=new Et.Root({props:{$$slots:{default:[oo]},$$scope:{ctx:t}}}),{c(){j(e.$$.fragment)},m(r,a){I(e,r,a),n=!0,o||(i=te(window,"message",t[5]),o=!0)},p(r,[a]){const c={};524319&a&&(c.$$scope={dirty:a,ctx:r}),e.$set(c)},i(r){n||(N(e.$$.fragment,r),n=!0)},o(r){_(e.$$.fragment,r),n=!1},d(r){F(e,r),o=!1,i()}}}function ht(t){if(!("prompt"in t))throw new Error("wrong type");if("completions"in t)throw new Error("wrong type");return t}function ro(t,e,n){let o,i,r,a,c,s={},l={},u={};function d(g){for(const p of g)s[p.requestId]||n(7,s[p.requestId]={...p,occuredAt:ke(p.occuredAt)},s);fe.cleanupFeedback(s)}function m(g){for(const p of g)if(!l[p.requestId]){if(typeof p.occuredAt=="string"){const M=p.occuredAt;p.occuredAt=ke(M)}n(8,l[p.requestId]={...p},l)}}function f(g){for(const p of g)p.suggestions.length!==0&&n(9,u[p.requestId]={requestId:p.requestId,occuredAt:ke(p.occurredAt),result:p},u)}oe.postMessage({type:Z.historyLoaded});let h=[],v="Completions";return t.$$.update=()=>{896&t.$$.dirty&&n(0,h=[...Object.values(l),...Object.values(s),...Object.values(u)].sort((g,p)=>p.occuredAt.getTime()-g.occuredAt.getTime())),1&t.$$.dirty&&n(11,o=h.filter(g=>"completions"in g)),1&t.$$.dirty&&n(10,i=h.filter(g=>"result"in g)),1&t.$$.dirty&&n(4,r=h.filter(g=>"prompt"in g)),3072&t.$$.dirty&&n(3,a=[{value:"Completions",label:`Completions ${o.length}`},{value:"Next Edits",label:`Next Edits ${i.length}`}]),3074&t.$$.dirty&&n(2,c=v==="Completions"?o:i)},[h,v,c,a,r,function(g){const p=g.data;switch(p.type){case Z.historyInitialize:m(p.data.instructions),d(p.data.completionRequests),f(p.data.nextEdits);break;case Z.completions:d(p.data);break;case Z.instructions:m(p.data);break;case Z.nextEditSuggestions:f([p.data])}},function(g){return n(1,v=g),!0},s,l,u,i,o]}new class extends re{constructor(t){super(),ae(this,t,ro,io,se,{})}}({target:document.getElementById("app")});
