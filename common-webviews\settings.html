<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Augment - Tool Configuration</title>
    <script nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
/**
 * Monaco bootstrap script
 *
 * This script is included directly in HTML files to load Monaco editor.
 * It's kept as a simple JS file to avoid any build/transpilation requirements.
 */

// Define the Monaco CDN version
const MONACO_VERSION = "0.52.2";
const MONACO_CDN_BASE = `https://cdnjs.cloudflare.com/ajax/libs/monaco-editor/${MONACO_VERSION}/min`;

// Initialize augmentDeps if it doesn't exist
window.augmentDeps = window.augmentDeps || {};

// Create a promise that will resolve when Monaco is ready
let monacoResolve;
window.augmentDeps.monaco = new Promise((resolve) => {
  monacoResolve = resolve;
});

// If Monaco is already loaded, don't load it again
if (window.monaco) {
  console.log("Monaco already loaded, skipping bootstrap");
  initializeMonacoDeps();
} else {
  // Load the Monaco loader script
  const loaderScript = document.createElement("script");
  loaderScript.src = `${MONACO_CDN_BASE}/vs/loader.min.js`;
  loaderScript.onload = initializeMonaco;
  document.head.appendChild(loaderScript);
}

// Initialize Monaco after the loader script has loaded
function initializeMonaco() {
  // require is provided by loader.min.js
  require.config({
    paths: { vs: `${MONACO_CDN_BASE}/vs` },
  });

  require(["vs/editor/editor.main"], () => {
    initializeMonacoDeps();
  });
}

// Initialize Monaco dependencies after Monaco has loaded
function initializeMonacoDeps() {
  // Resolve the monaco promise
  if (monacoResolve) {
    monacoResolve(window.monaco);
  }
}

</script>
    <meta property="csp-nonce" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <script type="module" crossorigin src="./assets/settings-C-2suv0G.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ=="></script>
    <link rel="modulepreload" crossorigin href="./assets/SpinnerAugment-CL9SZpf8.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/IconButtonAugment-C4xMcLhX.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/async-messaging-CtwQrvzD.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/message-broker-SEbJxN6J.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/CardAugment-bwPj7Y67.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/BaseTextInput-BAWt2_LS.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-BAWb-tvr.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/trash-can-UidoMBR_.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/design-system-init-SyQ8NwYv.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/mcp-logo-BBF9ZFwB.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/VSCodeCodicon-BxoMn_1r.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/keypress-DD1aQVr0.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-D2Ut0gK2.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/ellipsis-B3ZqaMmA.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/Drawer-DIQAznt-.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/ButtonAugment-iwbEjzvh.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/CalloutAugment-C-hloZHD.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/pen-to-square-DY0HDzb8.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/TextAreaAugment-CiMTZgUO.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/copy-BFy87Ryv.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/CollapseButtonAugment-Ba7Np-LE.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/types-CGlLNakm.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/file-paths-BPg3etNg.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/arrow-up-right-from-square-DUrpll74.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-BskWw2a8.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/index-iuo-Ho0S.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/rules-model-CkXHaFKK.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/chat-flags-model-LXed7yM_.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/RulesModeSelector-Cw1z9neV.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="modulepreload" crossorigin href="./assets/ModalAugment-DoMcZLcQ.js" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/SpinnerAugment-DnPofOlT.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/trash-can-B4Mu-RYe.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/design-system-init-BrD4yHQ_.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/IconButtonAugment-vCST1yxq.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-BYh6wkDD.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/VSCodeCodicon-DVaocTud.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/Drawer-DwFbLE28.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/ButtonAugment-zn72hvQy.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/CardAugment-BUhwqDnM.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/CalloutAugment-bpLK60vI.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/TextAreaAugment-J75lFxU7.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/copy-Do68LBjG.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/CollapseButtonAugment-CLLTFP8m.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/index-McRKs1sU.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/BaseTextInput-DuK8hbkY.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/rules-model-B6vv3aGc.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/RulesModeSelector-Qv_62MPy.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/ModalAugment-CM3byOYD.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
    <link rel="stylesheet" crossorigin href="./assets/settings-CPDC5_EC.css" nonce="nonce-63X+hNsdOFS2eV3h3qvqRQ==">
  </head>
  <body>
    <div id="app"></div>
  </body>
</html>
