import{m as o}from"./LanguageIcon-BQz1eaw5.js";import"./preload-helper-Dv6uf1Os.js";import"./SpinnerAugment-CL9SZpf8.js";var l=Object.defineProperty,a=Object.getOwnPropertyDescriptor,p=Object.getOwnPropertyNames,c=Object.prototype.hasOwnProperty,s={};((n,e,i,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let t of p(e))c.call(n,t)||t===i||l(n,t,{get:()=>e[t],enumerable:!(r=a(e,t))||r.enumerable})})(s,o,"default");var f={comments:{lineComment:"#",blockComment:["'''","'''"]},brackets:[["{","}"],["[","]"],["(",")"]],autoClosingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:'"',close:'"',notIn:["string"]},{open:"'",close:"'",notIn:["string","comment"]}],surroundingPairs:[{open:"{",close:"}"},{open:"[",close:"]"},{open:"(",close:")"},{open:'"',close:'"'},{open:"'",close:"'"}],onEnterRules:[{beforeText:new RegExp("^\\s*(?:def|class|for|if|elif|else|while|try|with|finally|except|async|match|case).*?:\\s*$"),action:{indentAction:s.languages.IndentAction.Indent}}],folding:{offSide:!0,markers:{start:new RegExp("^\\s*#region\\b"),end:new RegExp("^\\s*#endregion\\b")}}},_={defaultToken:"",tokenPostfix:".python",keywords:["False","None","True","_","and","as","assert","async","await","break","case","class","continue","def","del","elif","else","except","exec","finally","for","from","global","if","import","in","is","lambda","match","nonlocal","not","or","pass","print","raise","return","try","type","while","with","yield","int","float","long","complex","hex","abs","all","any","apply","basestring","bin","bool","buffer","bytearray","callable","chr","classmethod","cmp","coerce","compile","complex","delattr","dict","dir","divmod","enumerate","eval","execfile","file","filter","format","frozenset","getattr","globals","hasattr","hash","help","id","input","intern","isinstance","issubclass","iter","len","locals","list","map","max","memoryview","min","next","object","oct","open","ord","pow","print","property","reversed","range","raw_input","reduce","reload","repr","reversed","round","self","set","setattr","slice","sorted","staticmethod","str","sum","super","tuple","type","unichr","unicode","vars","xrange","zip","__dict__","__methods__","__members__","__class__","__bases__","__name__","__mro__","__subclasses__","__init__","__import__"],brackets:[{open:"{",close:"}",token:"delimiter.curly"},{open:"[",close:"]",token:"delimiter.bracket"},{open:"(",close:")",token:"delimiter.parenthesis"}],tokenizer:{root:[{include:"@whitespace"},{include:"@numbers"},{include:"@strings"},[/[,:;]/,"delimiter"],[/[{}\[\]()]/,"@brackets"],[/@[a-zA-Z_]\w*/,"tag"],[/[a-zA-Z_]\w*/,{cases:{"@keywords":"keyword","@default":"identifier"}}]],whitespace:[[/\s+/,"white"],[/(^#.*$)/,"comment"],[/'''/,"string","@endDocString"],[/"""/,"string","@endDblDocString"]],endDocString:[[/[^']+/,"string"],[/\\'/,"string"],[/'''/,"string","@popall"],[/'/,"string"]],endDblDocString:[[/[^"]+/,"string"],[/\\"/,"string"],[/"""/,"string","@popall"],[/"/,"string"]],numbers:[[/-?0x([abcdef]|[ABCDEF]|\d)+[lL]?/,"number.hex"],[/-?(\d*\.)?\d+([eE][+\-]?\d+)?[jJ]?[lL]?/,"number"]],strings:[[/'$/,"string.escape","@popall"],[/f'{1,3}/,"string.escape","@fStringBody"],[/'/,"string.escape","@stringBody"],[/"$/,"string.escape","@popall"],[/f"{1,3}/,"string.escape","@fDblStringBody"],[/"/,"string.escape","@dblStringBody"]],fStringBody:[[/[^\\'\{\}]+$/,"string","@popall"],[/[^\\'\{\}]+/,"string"],[/\{[^\}':!=]+/,"identifier","@fStringDetail"],[/\\./,"string"],[/'/,"string.escape","@popall"],[/\\$/,"string"]],stringBody:[[/[^\\']+$/,"string","@popall"],[/[^\\']+/,"string"],[/\\./,"string"],[/'/,"string.escape","@popall"],[/\\$/,"string"]],fDblStringBody:[[/[^\\"\{\}]+$/,"string","@popall"],[/[^\\"\{\}]+/,"string"],[/\{[^\}':!=]+/,"identifier","@fStringDetail"],[/\\./,"string"],[/"/,"string.escape","@popall"],[/\\$/,"string"]],dblStringBody:[[/[^\\"]+$/,"string","@popall"],[/[^\\"]+/,"string"],[/\\./,"string"],[/"/,"string.escape","@popall"],[/\\$/,"string"]],fStringDetail:[[/[:][^}]+/,"string"],[/[!][ars]/,"string"],[/=/,"string"],[/\}/,"identifier","@pop"]]}};export{f as conf,_ as language};
