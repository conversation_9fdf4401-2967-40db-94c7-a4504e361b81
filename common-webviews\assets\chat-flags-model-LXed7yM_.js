var et=Object.defineProperty;var tt=(t,e,s)=>e in t?et(t,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):t[e]=s;var o=(t,e,s)=>tt(t,typeof e!="symbol"?e+"":e,s);import{p as st,b as nt,c as at,n as D,W as rt,o as b,R as it,q as G,r as pe,t as X,u as be,v as Y,E as z,w as W,x as ot,y as lt}from"./index-BAWb-tvr.js";import{a as ct}from"./async-messaging-CtwQrvzD.js";import{W as d,S as ut}from"./IconButtonAugment-C4xMcLhX.js";import{P as T,C as dt}from"./message-broker-SEbJxN6J.js";import{n as ht}from"./file-paths-BPg3etNg.js";var E,c;function re(t,e){return!(t===null||typeof t!="object"||!("$typeName"in t)||typeof t.$typeName!="string")&&(e===void 0||e.typeName===t.$typeName)}function gt(){let t=0,e=0;for(let n=0;n<28;n+=7){let a=this.buf[this.pos++];if(t|=(127&a)<<n,!(128&a))return this.assertBounds(),[t,e]}let s=this.buf[this.pos++];if(t|=(15&s)<<28,e=(112&s)>>4,!(128&s))return this.assertBounds(),[t,e];for(let n=3;n<=31;n+=7){let a=this.buf[this.pos++];if(e|=(127&a)<<n,!(128&a))return this.assertBounds(),[t,e]}throw new Error("invalid varint")}(function(t){t[t.Canceled=1]="Canceled",t[t.Unknown=2]="Unknown",t[t.InvalidArgument=3]="InvalidArgument",t[t.DeadlineExceeded=4]="DeadlineExceeded",t[t.NotFound=5]="NotFound",t[t.AlreadyExists=6]="AlreadyExists",t[t.PermissionDenied=7]="PermissionDenied",t[t.ResourceExhausted=8]="ResourceExhausted",t[t.FailedPrecondition=9]="FailedPrecondition",t[t.Aborted=10]="Aborted",t[t.OutOfRange=11]="OutOfRange",t[t.Unimplemented=12]="Unimplemented",t[t.Internal=13]="Internal",t[t.Unavailable=14]="Unavailable",t[t.DataLoss=15]="DataLoss",t[t.Unauthenticated=16]="Unauthenticated"})(E||(E={})),function(t){t[t.DOUBLE=1]="DOUBLE",t[t.FLOAT=2]="FLOAT",t[t.INT64=3]="INT64",t[t.UINT64=4]="UINT64",t[t.INT32=5]="INT32",t[t.FIXED64=6]="FIXED64",t[t.FIXED32=7]="FIXED32",t[t.BOOL=8]="BOOL",t[t.STRING=9]="STRING",t[t.BYTES=12]="BYTES",t[t.UINT32=13]="UINT32",t[t.SFIXED32=15]="SFIXED32",t[t.SFIXED64=16]="SFIXED64",t[t.SINT32=17]="SINT32",t[t.SINT64=18]="SINT64"}(c||(c={}));const V=4294967296;function _e(t){const e=t[0]==="-";e&&(t=t.slice(1));const s=1e6;let n=0,a=0;function r(i,l){const u=Number(t.slice(i,l));a*=s,n=n*s+u,n>=V&&(a+=n/V|0,n%=V)}return r(-24,-18),r(-18,-12),r(-12,-6),r(-6),e?xe(n,a):ie(n,a)}function Se(t,e){if({lo:t,hi:e}=function(u,g){return{lo:u>>>0,hi:g>>>0}}(t,e),e<=2097151)return String(V*e+t);const s=16777215&(t>>>24|e<<8),n=e>>16&65535;let a=(16777215&t)+6777216*s+6710656*n,r=s+8147497*n,i=2*n;const l=1e7;return a>=l&&(r+=Math.floor(a/l),a%=l),r>=l&&(i+=Math.floor(r/l),r%=l),i.toString()+ve(r)+ve(a)}function ie(t,e){return{lo:0|t,hi:0|e}}function xe(t,e){return e=~e,t?t=1+~t:e+=1,ie(t,e)}const ve=t=>{const e=String(t);return"0000000".slice(e.length)+e};function mt(){let t=this.buf[this.pos++],e=127&t;if(!(128&t))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(127&t)<<7,!(128&t))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(127&t)<<14,!(128&t))return this.assertBounds(),e;if(t=this.buf[this.pos++],e|=(127&t)<<21,!(128&t))return this.assertBounds(),e;t=this.buf[this.pos++],e|=(15&t)<<28;for(let s=5;128&t&&s<10;s++)t=this.buf[this.pos++];if(128&t)throw new Error("invalid varint");return this.assertBounds(),e>>>0}var we={};const v=ft();function ft(){const t=new DataView(new ArrayBuffer(8));if(typeof BigInt=="function"&&typeof t.getBigInt64=="function"&&typeof t.getBigUint64=="function"&&typeof t.setBigInt64=="function"&&typeof t.setBigUint64=="function"&&(typeof process!="object"||typeof we!="object"||we.BUF_BIGINT_DISABLE!=="1")){const e=BigInt("-9223372036854775808"),s=BigInt("9223372036854775807"),n=BigInt("0"),a=BigInt("18446744073709551615");return{zero:BigInt(0),supported:!0,parse(r){const i=typeof r=="bigint"?r:BigInt(r);if(i>s||i<e)throw new Error(`invalid int64: ${r}`);return i},uParse(r){const i=typeof r=="bigint"?r:BigInt(r);if(i>a||i<n)throw new Error(`invalid uint64: ${r}`);return i},enc(r){return t.setBigInt64(0,this.parse(r),!0),{lo:t.getInt32(0,!0),hi:t.getInt32(4,!0)}},uEnc(r){return t.setBigInt64(0,this.uParse(r),!0),{lo:t.getInt32(0,!0),hi:t.getInt32(4,!0)}},dec:(r,i)=>(t.setInt32(0,r,!0),t.setInt32(4,i,!0),t.getBigInt64(0,!0)),uDec:(r,i)=>(t.setInt32(0,r,!0),t.setInt32(4,i,!0),t.getBigUint64(0,!0))}}return{zero:"0",supported:!1,parse:e=>(typeof e!="string"&&(e=e.toString()),Te(e),e),uParse:e=>(typeof e!="string"&&(e=e.toString()),Me(e),e),enc:e=>(typeof e!="string"&&(e=e.toString()),Te(e),_e(e)),uEnc:e=>(typeof e!="string"&&(e=e.toString()),Me(e),_e(e)),dec:(e,s)=>function(n,a){let r=ie(n,a);const i=2147483648&r.hi;i&&(r=xe(r.lo,r.hi));const l=Se(r.lo,r.hi);return i?"-"+l:l}(e,s),uDec:(e,s)=>Se(e,s)}}function Te(t){if(!/^-?[0-9]+$/.test(t))throw new Error("invalid int64: "+t)}function Me(t){if(!/^[0-9]+$/.test(t))throw new Error("invalid uint64: "+t)}function O(t,e){switch(t){case c.STRING:return"";case c.BOOL:return!1;case c.DOUBLE:case c.FLOAT:return 0;case c.INT64:case c.UINT64:case c.SFIXED64:case c.FIXED64:case c.SINT64:return e?"0":v.zero;case c.BYTES:return new Uint8Array(0);default:return 0}}const C=Symbol.for("reflect unsafe local");function De(t,e){const s=t[e.localName].case;return s===void 0?s:e.fields.find(n=>n.localName===s)}function yt(t,e){const s=e.localName;if(e.oneof)return t[e.oneof.localName].case===s;if(e.presence!=2)return t[s]!==void 0&&Object.prototype.hasOwnProperty.call(t,s);switch(e.fieldKind){case"list":return t[s].length>0;case"map":return Object.keys(t[s]).length>0;case"scalar":return!function(n,a){switch(n){case c.BOOL:return a===!1;case c.STRING:return a==="";case c.BYTES:return a instanceof Uint8Array&&!a.byteLength;default:return a==0}}(e.scalar,t[s]);case"enum":return t[s]!==e.enum.values[0].number}throw new Error("message field with implicit presence")}function Ue(t,e){if(e.oneof){const s=t[e.oneof.localName];return s.case===e.localName?s.value:void 0}return t[e.localName]}function Be(t,e,s){e.oneof?t[e.oneof.localName]={case:e.localName,value:s}:t[e.localName]=s}function N(t){return t!==null&&typeof t=="object"&&!Array.isArray(t)}function ne(t,e){var s,n,a,r;if(N(t)&&C in t&&"add"in t&&"field"in t&&typeof t.field=="function"){if(e!==void 0){const i=e,l=t.field();return i.listKind==l.listKind&&i.scalar===l.scalar&&((s=i.message)===null||s===void 0?void 0:s.typeName)===((n=l.message)===null||n===void 0?void 0:n.typeName)&&((a=i.enum)===null||a===void 0?void 0:a.typeName)===((r=l.enum)===null||r===void 0?void 0:r.typeName)}return!0}return!1}function ae(t,e){var s,n,a,r;if(N(t)&&C in t&&"has"in t&&"field"in t&&typeof t.field=="function"){if(e!==void 0){const i=e,l=t.field();return i.mapKey===l.mapKey&&i.mapKind==l.mapKind&&i.scalar===l.scalar&&((s=i.message)===null||s===void 0?void 0:s.typeName)===((n=l.message)===null||n===void 0?void 0:n.typeName)&&((a=i.enum)===null||a===void 0?void 0:a.typeName)===((r=l.enum)===null||r===void 0?void 0:r.typeName)}return!0}return!1}function oe(t,e){return N(t)&&C in t&&"desc"in t&&N(t.desc)&&t.desc.kind==="message"&&(e===void 0||t.desc.typeName==e.typeName)}function Oe(t){const e=t.fields[0];return qe(t.typeName)&&e!==void 0&&e.fieldKind=="scalar"&&e.name=="value"&&e.number==1}function qe(t){return t.startsWith("google.protobuf.")&&["DoubleValue","FloatValue","Int64Value","UInt64Value","Int32Value","UInt32Value","BoolValue","StringValue","BytesValue"].includes(t.substring(16))}const pt=999,bt=998,L=2;function le(t,e){if(re(e,t))return e;const s=function(n){let a;if(function(r){switch(r.file.edition){case pt:return!1;case bt:return!0;default:return r.fields.some(i=>i.presence!=L&&i.fieldKind!="message"&&!i.oneof)}}(n)){const r=ke.get(n);let i,l;if(r)({prototype:i,members:l}=r);else{i={},l=new Set;for(const u of n.members)u.kind!="oneof"&&(u.fieldKind!="scalar"&&u.fieldKind!="enum"||u.presence!=L&&(l.add(u),i[u.localName]=Z(u)));ke.set(n,{prototype:i,members:l})}a=Object.create(i),a.$typeName=n.typeName;for(const u of n.members)if(!l.has(u)){if(u.kind=="field"&&(u.fieldKind=="message"||(u.fieldKind=="scalar"||u.fieldKind=="enum")&&u.presence!=L))continue;a[u.localName]=Z(u)}}else{a={$typeName:n.typeName};for(const r of n.members)r.kind!="oneof"&&r.presence!=L||(a[r.localName]=Z(r))}return a}(t);return e!==void 0&&function(n,a,r){for(const i of n.members){let l,u=r[i.localName];if(u!=null){if(i.kind=="oneof"){const g=De(r,i);if(!g)continue;l=g,u=Ue(r,g)}else l=i;switch(l.fieldKind){case"message":u=ce(l,u);break;case"scalar":u=Le(l,u);break;case"list":u=St(l,u);break;case"map":u=_t(l,u)}Be(a,l,u)}}}(t,s,e),s}function Le(t,e){return t.scalar==c.BYTES?ue(e):e}function _t(t,e){if(N(e)){if(t.scalar==c.BYTES)return Ie(e,ue);if(t.mapKind=="message")return Ie(e,s=>ce(t,s))}return e}function St(t,e){if(Array.isArray(e)){if(t.scalar==c.BYTES)return e.map(ue);if(t.listKind=="message")return e.map(s=>ce(t,s))}return e}function ce(t,e){if(t.fieldKind=="message"&&!t.oneof&&Oe(t.message))return Le(t.message.fields[0],e);if(N(e)){if(t.message.typeName=="google.protobuf.Struct"&&t.parent.typeName!=="google.protobuf.Value")return e;if(!re(e,t.message))return le(t.message,e)}return e}function ue(t){return Array.isArray(t)?new Uint8Array(t):t}function Ie(t,e){const s={};for(const n of Object.entries(t))s[n[0]]=e(n[1]);return s}const vt=Symbol(),ke=new WeakMap;function Z(t){if(t.kind=="oneof")return{case:void 0};if(t.fieldKind=="list")return[];if(t.fieldKind=="map")return{};if(t.fieldKind=="message")return vt;const e=t.getDefaultValue();return e!==void 0?t.fieldKind=="scalar"&&t.longAsString?e.toString():e:t.fieldKind=="scalar"?O(t.scalar,t.longAsString):t.enum.values[0].number}class F extends Error{constructor(e,s,n="FieldValueInvalidError"){super(s),this.name=n,this.field=()=>e}}const Q=Symbol.for("@bufbuild/protobuf/text-encoding");function $e(){if(globalThis[Q]==null){const t=new globalThis.TextEncoder,e=new globalThis.TextDecoder;globalThis[Q]={encodeUtf8:s=>t.encode(s),decodeUtf8:s=>e.decode(s),checkUtf8(s){try{return encodeURIComponent(s),!0}catch{return!1}}}}return globalThis[Q]}var M;(function(t){t[t.Varint=0]="Varint",t[t.Bit64=1]="Bit64",t[t.LengthDelimited=2]="LengthDelimited",t[t.StartGroup=3]="StartGroup",t[t.EndGroup=4]="EndGroup",t[t.Bit32=5]="Bit32"})(M||(M={}));const wt=34028234663852886e22,Tt=-34028234663852886e22,Mt=4294967295,It=2147483647,kt=-2147483648;class Ct{constructor(e,s=$e().decodeUtf8){this.decodeUtf8=s,this.varint64=gt,this.uint32=mt,this.buf=e,this.len=e.length,this.pos=0,this.view=new DataView(e.buffer,e.byteOffset,e.byteLength)}tag(){let e=this.uint32(),s=e>>>3,n=7&e;if(s<=0||n<0||n>5)throw new Error("illegal tag: field no "+s+" wire type "+n);return[s,n]}skip(e,s){let n=this.pos;switch(e){case M.Varint:for(;128&this.buf[this.pos++];);break;case M.Bit64:this.pos+=4;case M.Bit32:this.pos+=4;break;case M.LengthDelimited:let a=this.uint32();this.pos+=a;break;case M.StartGroup:for(;;){const[r,i]=this.tag();if(i===M.EndGroup){if(s!==void 0&&r!==s)throw new Error("invalid end group tag");break}this.skip(i,r)}break;default:throw new Error("cant skip wire type "+e)}return this.assertBounds(),this.buf.subarray(n,this.pos)}assertBounds(){if(this.pos>this.len)throw new RangeError("premature EOF")}int32(){return 0|this.uint32()}sint32(){let e=this.uint32();return e>>>1^-(1&e)}int64(){return v.dec(...this.varint64())}uint64(){return v.uDec(...this.varint64())}sint64(){let[e,s]=this.varint64(),n=-(1&e);return e=(e>>>1|(1&s)<<31)^n,s=s>>>1^n,v.dec(e,s)}bool(){let[e,s]=this.varint64();return e!==0||s!==0}fixed32(){return this.view.getUint32((this.pos+=4)-4,!0)}sfixed32(){return this.view.getInt32((this.pos+=4)-4,!0)}fixed64(){return v.uDec(this.sfixed32(),this.sfixed32())}sfixed64(){return v.dec(this.sfixed32(),this.sfixed32())}float(){return this.view.getFloat32((this.pos+=4)-4,!0)}double(){return this.view.getFloat64((this.pos+=8)-8,!0)}bytes(){let e=this.uint32(),s=this.pos;return this.pos+=e,this.assertBounds(),this.buf.subarray(s,s+e)}string(){return this.decodeUtf8(this.bytes())}}function Ce(t,e,s){const n=de(t,s);if(n!==!0)return new F(t,`list item #${e+1}: ${j(t,s,n)}`)}function de(t,e){return t.scalar!==void 0?Ve(e,t.scalar):t.enum!==void 0?t.enum.open?Number.isInteger(e):t.enum.values.some(s=>s.number===e):oe(e,t.message)}function Ve(t,e){switch(e){case c.DOUBLE:return typeof t=="number";case c.FLOAT:return typeof t=="number"&&(!(!Number.isNaN(t)&&Number.isFinite(t))||!(t>wt||t<Tt)||`${t.toFixed()} out of range`);case c.INT32:case c.SFIXED32:case c.SINT32:return!(typeof t!="number"||!Number.isInteger(t))&&(!(t>It||t<kt)||`${t.toFixed()} out of range`);case c.FIXED32:case c.UINT32:return!(typeof t!="number"||!Number.isInteger(t))&&(!(t>Mt||t<0)||`${t.toFixed()} out of range`);case c.BOOL:return typeof t=="boolean";case c.STRING:return typeof t=="string"&&($e().checkUtf8(t)||"invalid UTF8");case c.BYTES:return t instanceof Uint8Array;case c.INT64:case c.SFIXED64:case c.SINT64:if(typeof t=="bigint"||typeof t=="number"||typeof t=="string"&&t.length>0)try{return v.parse(t),!0}catch{return`${t} out of range`}return!1;case c.FIXED64:case c.UINT64:if(typeof t=="bigint"||typeof t=="number"||typeof t=="string"&&t.length>0)try{return v.uParse(t),!0}catch{return`${t} out of range`}return!1}}function j(t,e,s){return s=typeof s=="string"?`: ${s}`:`, got ${H(e)}`,t.scalar!==void 0?`expected ${function(n){switch(n){case c.STRING:return"string";case c.BOOL:return"boolean";case c.INT64:case c.SINT64:case c.SFIXED64:return"bigint (int64)";case c.UINT64:case c.FIXED64:return"bigint (uint64)";case c.BYTES:return"Uint8Array";case c.DOUBLE:return"number (float64)";case c.FLOAT:return"number (float32)";case c.FIXED32:case c.UINT32:return"number (uint32)";case c.INT32:case c.SFIXED32:case c.SINT32:return"number (int32)"}}(t.scalar)}`+s:t.enum!==void 0?`expected ${t.enum.toString()}`+s:`expected ${je(t.message)}`+s}function H(t){switch(typeof t){case"object":return t===null?"null":t instanceof Uint8Array?`Uint8Array(${t.length})`:Array.isArray(t)?`Array(${t.length})`:ne(t)?He(t.field()):ae(t)?Ke(t.field()):oe(t)?je(t.desc):re(t)?`message ${t.$typeName}`:"object";case"string":return t.length>30?"string":`"${t.split('"').join('\\"')}"`;case"boolean":case"number":return String(t);case"bigint":return String(t)+"n";default:return typeof t}}function je(t){return`ReflectMessage (${t.typeName})`}function He(t){switch(t.listKind){case"message":return`ReflectList (${t.message.toString()})`;case"enum":return`ReflectList (${t.enum.toString()})`;case"scalar":return`ReflectList (${c[t.scalar]})`}}function Ke(t){switch(t.mapKind){case"message":return`ReflectMap (${c[t.mapKey]}, ${t.message.toString()})`;case"enum":return`ReflectMap (${c[t.mapKey]}, ${t.enum.toString()})`;case"scalar":return`ReflectMap (${c[t.mapKey]}, ${c[t.scalar]})`}}function he(t,e,s=!0){return new Ge(t,e,s)}class Ge{get sortedFields(){var e;return(e=this._sortedFields)!==null&&e!==void 0?e:this._sortedFields=this.desc.fields.concat().sort((s,n)=>s.number-n.number)}constructor(e,s,n=!0){this.lists=new Map,this.maps=new Map,this.check=n,this.desc=e,this.message=this[C]=s??le(e),this.fields=e.fields,this.oneofs=e.oneofs,this.members=e.members}findNumber(e){return this._fieldsByNumber||(this._fieldsByNumber=new Map(this.desc.fields.map(s=>[s.number,s]))),this._fieldsByNumber.get(e)}oneofCase(e){return U(this.message,e),De(this.message,e)}isSet(e){return U(this.message,e),yt(this.message,e)}clear(e){U(this.message,e),function(s,n){const a=n.localName;if(n.oneof){const r=n.oneof.localName;s[r].case===a&&(s[r]={case:void 0})}else if(n.presence!=2)delete s[a];else switch(n.fieldKind){case"map":s[a]={};break;case"list":s[a]=[];break;case"enum":s[a]=n.enum.values[0].number;break;case"scalar":s[a]=O(n.scalar,n.longAsString)}}(this.message,e)}get(e){U(this.message,e);const s=Ue(this.message,e);switch(e.fieldKind){case"list":let n=this.lists.get(e);return n&&n[C]===s||this.lists.set(e,n=new Rt(e,s,this.check)),n;case"map":let a=this.maps.get(e);return a&&a[C]===s||this.maps.set(e,a=new Et(e,s,this.check)),a;case"message":return me(e,s,this.check);case"scalar":return s===void 0?O(e.scalar,!1):fe(e,s);case"enum":return s??e.enum.values[0].number}}set(e,s){if(U(this.message,e),this.check){const a=function(r,i){const l=r.fieldKind=="list"?ne(i,r):r.fieldKind=="map"?ae(i,r):de(r,i);if(l===!0)return;let u;switch(r.fieldKind){case"list":u=`expected ${He(r)}, got ${H(i)}`;break;case"map":u=`expected ${Ke(r)}, got ${H(i)}`;break;default:u=j(r,i,l)}return new F(r,u)}(e,s);if(a)throw a}let n;n=e.fieldKind=="message"?ge(e,s):ae(s)||ne(s)?s[C]:ye(e,s),Be(this.message,e,n)}getUnknown(){return this.message.$unknown}setUnknown(e){this.message.$unknown=e}}function U(t,e){if(e.parent.typeName!==t.$typeName)throw new F(e,`cannot use ${e.toString()} with message ${t.$typeName}`,"ForeignFieldError")}class Rt{field(){return this._field}get size(){return this._arr.length}constructor(e,s,n){this._field=e,this._arr=this[C]=s,this.check=n}get(e){const s=this._arr[e];return s===void 0?void 0:J(this._field,s,this.check)}set(e,s){if(e<0||e>=this._arr.length)throw new F(this._field,`list item #${e+1}: out of range`);if(this.check){const n=Ce(this._field,e,s);if(n)throw n}this._arr[e]=Re(this._field,s)}add(e){if(this.check){const s=Ce(this._field,this._arr.length,e);if(s)throw s}this._arr.push(Re(this._field,e))}clear(){this._arr.splice(0,this._arr.length)}[Symbol.iterator](){return this.values()}keys(){return this._arr.keys()}*values(){for(const e of this._arr)yield J(this._field,e,this.check)}*entries(){for(let e=0;e<this._arr.length;e++)yield[e,J(this._field,this._arr[e],this.check)]}}class Et{constructor(e,s,n=!0){this.obj=this[C]=s??{},this.check=n,this._field=e}field(){return this._field}set(e,s){if(this.check){const n=function(a,r,i){const l=Ve(r,a.mapKey);if(l!==!0)return new F(a,`invalid map key: ${j({scalar:a.mapKey},r,l)}`);const u=de(a,i);return u!==!0?new F(a,`map entry ${H(r)}: ${j(a,i,u)}`):void 0}(this._field,e,s);if(n)throw n}return this.obj[$(e)]=function(n,a){return n.mapKind=="message"?ge(n,a):ye(n,a)}(this._field,s),this}delete(e){const s=$(e),n=Object.prototype.hasOwnProperty.call(this.obj,s);return n&&delete this.obj[s],n}clear(){for(const e of Object.keys(this.obj))delete this.obj[e]}get(e){let s=this.obj[$(e)];return s!==void 0&&(s=ee(this._field,s,this.check)),s}has(e){return Object.prototype.hasOwnProperty.call(this.obj,$(e))}*keys(){for(const e of Object.keys(this.obj))yield Ee(e,this._field.mapKey)}*entries(){for(const e of Object.entries(this.obj))yield[Ee(e[0],this._field.mapKey),ee(this._field,e[1],this.check)]}[Symbol.iterator](){return this.entries()}get size(){return Object.keys(this.obj).length}*values(){for(const e of Object.values(this.obj))yield ee(this._field,e,this.check)}forEach(e,s){for(const n of this.entries())e.call(s,n[1],n[0],this)}}function ge(t,e){return oe(e)?qe(e.message.$typeName)&&!t.oneof&&t.fieldKind=="message"?e.message.value:e.desc.typeName=="google.protobuf.Struct"&&t.parent.typeName!="google.protobuf.Value"?Ye(e.message):e.message:e}function me(t,e,s){return e!==void 0&&(Oe(t.message)&&!t.oneof&&t.fieldKind=="message"?e={$typeName:t.message.typeName,value:fe(t.message.fields[0],e)}:t.message.typeName=="google.protobuf.Struct"&&t.parent.typeName!="google.protobuf.Value"&&N(e)&&(e=Xe(e))),new Ge(t.message,e,s)}function Re(t,e){return t.listKind=="message"?ge(t,e):ye(t,e)}function J(t,e,s){return t.listKind=="message"?me(t,e,s):fe(t,e)}function ee(t,e,s){return t.mapKind=="message"?me(t,e,s):e}function $(t){return typeof t=="string"||typeof t=="number"?t:String(t)}function Ee(t,e){switch(e){case c.STRING:return t;case c.INT32:case c.FIXED32:case c.UINT32:case c.SFIXED32:case c.SINT32:{const s=Number.parseInt(t);if(Number.isFinite(s))return s;break}case c.BOOL:switch(t){case"true":return!0;case"false":return!1}break;case c.UINT64:case c.FIXED64:try{return v.uParse(t)}catch{}break;default:try{return v.parse(t)}catch{}}return t}function fe(t,e){switch(t.scalar){case c.INT64:case c.SFIXED64:case c.SINT64:"longAsString"in t&&t.longAsString&&typeof e=="string"&&(e=v.parse(e));break;case c.FIXED64:case c.UINT64:"longAsString"in t&&t.longAsString&&typeof e=="string"&&(e=v.uParse(e))}return e}function ye(t,e){switch(t.scalar){case c.INT64:case c.SFIXED64:case c.SINT64:"longAsString"in t&&t.longAsString?e=String(e):typeof e!="string"&&typeof e!="number"||(e=v.parse(e));break;case c.FIXED64:case c.UINT64:"longAsString"in t&&t.longAsString?e=String(e):typeof e!="string"&&typeof e!="number"||(e=v.uParse(e))}return e}function Xe(t){const e={$typeName:"google.protobuf.Struct",fields:{}};if(N(t))for(const[s,n]of Object.entries(t))e.fields[s]=We(n);return e}function Ye(t){const e={};for(const[s,n]of Object.entries(t.fields))e[s]=ze(n);return e}function ze(t){switch(t.kind.case){case"structValue":return Ye(t.kind.value);case"listValue":return t.kind.value.values.map(ze);case"nullValue":case void 0:return null;default:return t.kind.value}}function We(t){const e={$typeName:"google.protobuf.Value",kind:{case:void 0}};switch(typeof t){case"number":e.kind={case:"numberValue",value:t};break;case"string":e.kind={case:"stringValue",value:t};break;case"boolean":e.kind={case:"boolValue",value:t};break;case"object":if(t===null)e.kind={case:"nullValue",value:0};else if(Array.isArray(t)){const s={$typeName:"google.protobuf.ListValue",values:[]};if(Array.isArray(t))for(const n of t)s.values.push(We(n));e.kind={case:"listValue",value:s}}else e.kind={case:"structValue",value:Xe(t)}}return e}const Ne={readUnknownFields:!0};function Nt(t,e,s){const n=he(t,void 0,!1);return Ze(n,new Ct(e),function(a){return a?Object.assign(Object.assign({},Ne),a):Ne}(s),!1,e.byteLength),n.message}function Ze(t,e,s,n,a){var r;const i=n?e.len:e.pos+a;let l,u;const g=(r=t.getUnknown())!==null&&r!==void 0?r:[];for(;e.pos<i&&([l,u]=e.tag(),!n||u!=M.EndGroup);){const m=t.findNumber(l);if(m)At(t,e,m,u,s);else{const h=e.skip(u,l);s.readUnknownFields&&g.push({no:l,wireType:u,data:h})}}if(n&&(u!=M.EndGroup||l!==a))throw new Error("invalid end group tag");g.length>0&&t.setUnknown(g)}function At(t,e,s,n,a){switch(s.fieldKind){case"scalar":t.set(s,A(e,s.scalar));break;case"enum":t.set(s,A(e,c.INT32));break;case"message":t.set(s,te(e,a,s,t.get(s)));break;case"list":(function(r,i,l,u){var g;const m=l.field();if(m.listKind==="message")return void l.add(te(r,u,m));const h=(g=m.scalar)!==null&&g!==void 0?g:c.INT32;if(!(i==M.LengthDelimited&&h!=c.STRING&&h!=c.BYTES))return void l.add(A(r,h));const p=r.uint32()+r.pos;for(;r.pos<p;)l.add(A(r,h))})(e,n,t.get(s),a);break;case"map":(function(r,i,l){const u=i.field();let g,m;const h=r.pos+r.uint32();for(;r.pos<h;){const[f]=r.tag();switch(f){case 1:g=A(r,u.mapKey);break;case 2:switch(u.mapKind){case"scalar":m=A(r,u.scalar);break;case"enum":m=r.int32();break;case"message":m=te(r,l,u)}}}if(g===void 0&&(g=O(u.mapKey,!1)),m===void 0)switch(u.mapKind){case"scalar":m=O(u.scalar,!1);break;case"enum":m=u.enum.values[0].number;break;case"message":m=he(u.message,void 0,!1)}i.set(g,m)})(e,t.get(s),a)}}function te(t,e,s,n){const a=s.delimitedEncoding,r=n??he(s.message,void 0,!1);return Ze(r,t,e,a,a?s.number:t.uint32()),r}function A(t,e){switch(e){case c.STRING:return t.string();case c.BOOL:return t.bool();case c.DOUBLE:return t.double();case c.FLOAT:return t.float();case c.INT32:return t.int32();case c.INT64:return t.int64();case c.UINT64:return t.uint64();case c.FIXED64:return t.fixed64();case c.BYTES:return t.bytes();case c.FIXED32:return t.fixed32();case c.SFIXED32:return t.sfixed32();case c.SFIXED64:return t.sfixed64();case c.SINT64:return t.sint64();case c.UINT32:return t.uint32();case c.SINT32:return t.sint32()}}function Ae(t){const e=E[t];return typeof e!="string"?t.toString():e[0].toLowerCase()+e.substring(1).replace(/[A-Z]/g,s=>"_"+s.toLowerCase())}class R extends Error{constructor(e,s=E.Unknown,n,a,r){super(function(i,l){return i.length?`[${Ae(l)}] ${i}`:`[${Ae(l)}]`}(e,s)),this.name="ConnectError",Object.setPrototypeOf(this,new.target.prototype),this.rawMessage=e,this.code=s,this.metadata=new Headers(n??{}),this.details=a??[],this.cause=r}static from(e,s=E.Unknown){return e instanceof R?e:e instanceof Error?e.name=="AbortError"?new R(e.message,E.Canceled):new R(e.message,s,void 0,void 0,e):new R(String(e),s,void 0,void 0,e)}static[Symbol.hasInstance](e){return e instanceof Error&&(Object.getPrototypeOf(e)===R.prototype||e.name==="ConnectError"&&"code"in e&&typeof e.code=="number"&&"metadata"in e&&"details"in e&&Array.isArray(e.details)&&"rawMessage"in e&&typeof e.rawMessage=="string"&&"cause"in e)}findDetails(e){const s=e.kind==="message"?{getMessage:a=>a===e.typeName?e:void 0}:e,n=[];for(const a of this.details){if("desc"in a){s.getMessage(a.desc.typeName)&&n.push(le(a.desc,a.value));continue}const r=s.getMessage(a.type);if(r)try{n.push(Nt(r,a.value))}catch{}}return n}}var Ft=function(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,s=t[Symbol.asyncIterator];return s?s.call(t):(t=typeof __values=="function"?__values(t):t[Symbol.iterator](),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);function n(a){e[a]=t[a]&&function(r){return new Promise(function(i,l){(function(u,g,m,h){Promise.resolve(h).then(function(f){u({value:f,done:m})},g)})(i,l,(r=t[a](r)).done,r.value)})}}},q=function(t){return this instanceof q?(this.v=t,this):new q(t)},Pt=function(t,e,s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,a=s.apply(t,e||[]),r=[];return n=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(h){return function(f){return Promise.resolve(f).then(h,g)}}),n[Symbol.asyncIterator]=function(){return this},n;function i(h,f){a[h]&&(n[h]=function(p){return new Promise(function(_,k){r.push([h,p,_,k])>1||l(h,p)})},f&&(n[h]=f(n[h])))}function l(h,f){try{(p=a[h](f)).value instanceof q?Promise.resolve(p.value.v).then(u,g):m(r[0][2],p)}catch(_){m(r[0][3],_)}var p}function u(h){l("next",h)}function g(h){l("throw",h)}function m(h,f){h(f),r.shift(),r.length&&l(r[0][0],r[0][1])}},xt=function(t){var e,s;return e={},n("next"),n("throw",function(a){throw a}),n("return"),e[Symbol.iterator]=function(){return this},e;function n(a,r){e[a]=t[a]?function(i){return(s=!s)?{value:q(t[a](i)),done:!1}:r?r(i):i}:r}},Qe=function(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,s=t[Symbol.asyncIterator];return s?s.call(t):(t=typeof __values=="function"?__values(t):t[Symbol.iterator](),e={},n("next"),n("throw"),n("return"),e[Symbol.asyncIterator]=function(){return this},e);function n(a){e[a]=t[a]&&function(r){return new Promise(function(i,l){(function(u,g,m,h){Promise.resolve(h).then(function(f){u({value:f,done:m})},g)})(i,l,(r=t[a](r)).done,r.value)})}}},P=function(t){return this instanceof P?(this.v=t,this):new P(t)},Dt=function(t){var e,s;return e={},n("next"),n("throw",function(a){throw a}),n("return"),e[Symbol.iterator]=function(){return this},e;function n(a,r){e[a]=t[a]?function(i){return(s=!s)?{value:P(t[a](i)),done:!1}:r?r(i):i}:r}},Ut=function(t,e,s){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var n,a=s.apply(t,e||[]),r=[];return n=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),i("next"),i("throw"),i("return",function(h){return function(f){return Promise.resolve(f).then(h,g)}}),n[Symbol.asyncIterator]=function(){return this},n;function i(h,f){a[h]&&(n[h]=function(p){return new Promise(function(_,k){r.push([h,p,_,k])>1||l(h,p)})},f&&(n[h]=f(n[h])))}function l(h,f){try{(p=a[h](f)).value instanceof P?Promise.resolve(p.value.v).then(u,g):m(r[0][2],p)}catch(_){m(r[0][3],_)}var p}function u(h){l("next",h)}function g(h){l("throw",h)}function m(h,f){h(f),r.shift(),r.length&&l(r[0][0],r[0][1])}};function Bt(t,e){return function(s,n){const a={};for(const r of s.methods){const i=n(r);i!=null&&(a[r.localName]=i)}return a}(t,s=>{switch(s.methodKind){case"unary":return function(n,a){return async function(r,i){var l,u;const g=await n.unary(a,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues);return(l=i==null?void 0:i.onHeader)===null||l===void 0||l.call(i,g.header),(u=i==null?void 0:i.onTrailer)===null||u===void 0||u.call(i,g.trailer),g.message}}(e,s);case"server_streaming":return function(n,a){return function(r,i){return Fe(n.stream(a,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,function(l){return Pt(this,arguments,function*(){yield q(yield*xt(Ft(l)))})}([r]),i==null?void 0:i.contextValues),i)}}(e,s);case"client_streaming":return function(n,a){return async function(r,i){var l,u,g,m,h,f;const p=await n.stream(a,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues);let _;(h=i==null?void 0:i.onHeader)===null||h===void 0||h.call(i,p.header);let k=0;try{for(var y,x=!0,K=Qe(p.message);!(l=(y=await K.next()).done);x=!0)m=y.value,x=!1,_=m,k++}catch(Je){u={error:Je}}finally{try{x||l||!(g=K.return)||await g.call(K)}finally{if(u)throw u.error}}if(!_)throw new R("protocol error: missing response message",E.Unimplemented);if(k>1)throw new R("protocol error: received extra messages for client streaming method",E.Unimplemented);return(f=i==null?void 0:i.onTrailer)===null||f===void 0||f.call(i,p.trailer),_}}(e,s);case"bidi_streaming":return function(n,a){return function(r,i){return Fe(n.stream(a,i==null?void 0:i.signal,i==null?void 0:i.timeoutMs,i==null?void 0:i.headers,r,i==null?void 0:i.contextValues),i)}}(e,s);default:return null}})}function Fe(t,e){const s=function(){return Ut(this,arguments,function*(){var n,a;const r=yield P(t);(n=e==null?void 0:e.onHeader)===null||n===void 0||n.call(e,r.header),yield P(yield*Dt(Qe(r.message))),(a=e==null?void 0:e.onTrailer)===null||a===void 0||a.call(e,r.trailer)})}()[Symbol.asyncIterator]();return{[Symbol.asyncIterator]:()=>({next:()=>s.next()})}}function w(t,e){return e in t&&t[e]!==void 0}function Ot(t){return w(t,"file")}function qt(t){return w(t,"recentFile")}function Lt(t){return w(t,"folder")}function $t(t){return w(t,"sourceFolder")}function os(t){return w(t,"selection")}function Vt(t){return w(t,"externalSource")}function ls(t){return w(t,"allDefaultContext")}function cs(t){return w(t,"clearContext")}function us(t){return w(t,"userGuidelines")}function jt(t){return w(t,"personality")}function Ht(t){return w(t,"rule")}function ds(t){return w(t,"task")}const hs={allDefaultContext:!0,label:"Default Context",id:"allDefaultContext"},gs={clearContext:!0,label:"Clear Context",id:"clearContext"},ms={userGuidelines:{overLimit:!1,contents:"",lengthLimit:2e3},label:"User Guidelines",id:"userGuidelines"},fs={agentMemories:{},label:"Agent Memories",id:"agentMemories"},Pe=[{personality:{type:T.DEFAULT,description:"Expert software engineer - trusted coding agent, at your service!"},label:"Agent Auggie",name:"auggie-personality-agent-default",id:"auggie-personality-agent-default"},{personality:{type:T.PROTOTYPER,description:"Fast and loose - let's get it done, boss!"},label:"Prototyper Auggie",name:"auggie-personality-prototyper",id:"auggie-personality-prototyper"},{personality:{type:T.BRAINSTORM,description:"Thoughtful and creative - thinking through all possibilities..."},label:"Brainstorm Auggie",name:"auggie-personality-brainstorm",id:"auggie-personality-brainstorm"},{personality:{type:T.REVIEWER,description:"Code detective - finding issues and analyzing implications"},label:"Reviewer Auggie",name:"auggie-personality-reviewer",id:"auggie-personality-reviewer"}];function ys(t){return w(t,"group")}function ps(t){const e=new Map;return t.forEach(s=>{Ot(s)?e.set("file",[...e.get("file")??[],s]):qt(s)?e.set("recentFile",[...e.get("recentFile")??[],s]):Lt(s)?e.set("folder",[...e.get("folder")??[],s]):Vt(s)?e.set("externalSource",[...e.get("externalSource")??[],s]):$t(s)?e.set("sourceFolder",[...e.get("sourceFolder")??[],s]):jt(s)?e.set("personality",[...e.get("personality")??[],s]):Ht(s)&&e.set("rule",[...e.get("rule")??[],s])}),[{label:"Personalities",id:"personalities",group:{type:"personality",materialIcon:"person",items:e.get("personality")??[]}},{label:"Files",id:"files",group:{type:"file",materialIcon:"insert_drive_file",items:e.get("file")??[]}},{label:"Folders",id:"folders",group:{type:"folder",materialIcon:"folder",items:e.get("folder")??[]}},{label:"Source Folders",id:"sourceFolders",group:{type:"sourceFolder",materialIcon:"folder_managed",items:e.get("sourceFolder")??[]}},{label:"Recently Opened Files",id:"recentlyOpenedFiles",group:{type:"recentFile",materialIcon:"insert_drive_file",items:e.get("recentFile")??[]}},{label:"Documentation",id:"externalSources",group:{type:"externalSource",materialIcon:"link",items:e.get("externalSource")??[]}},{label:"Rules",id:"rules",group:{type:"rule",materialIcon:"rule",items:e.get("rule")??[]}}].filter(s=>s.group.items.length>0)}function Kt(t){const e=st({rootPath:t.repoRoot,relPath:t.pathName}),s={label:ht(t.pathName).split("/").filter(n=>n.trim()!=="").pop()||"",name:e,id:e};if(t.fullRange){const n=`:L${t.fullRange.startLineNumber}-${t.fullRange.endLineNumber}`;s.label+=n,s.name+=n,s.id+=n}else if(t.range){const n=`:L${t.range.start}-${t.range.stop}`;s.label+=n,s.name+=n,s.id+=n}return s}function Gt(t){const e=t.path.split("/"),s=e[e.length-1],n=s.endsWith(".md")?s.slice(0,-3):s,a=`${nt}/${at}/${t.path}`;return{label:n,name:a,id:a}}function bs(t,e){const s=t.customPersonalityPrompts;if(s)switch(e){case T.DEFAULT:if(s.agent&&s.agent.trim()!=="")return s.agent;break;case T.PROTOTYPER:if(s.prototyper&&s.prototyper.trim()!=="")return s.prototyper;break;case T.BRAINSTORM:if(s.brainstorm&&s.brainstorm.trim()!=="")return s.brainstorm;break;case T.REVIEWER:if(s.reviewer&&s.reviewer.trim()!=="")return s.reviewer}return Xt[e]}const Xt={[T.DEFAULT]:`
# Agent Auggie Personality Description
You are Augment Agent, an agentic coding AI assistant.
Focus on helping the user with their coding tasks efficiently.

## Rules:
- You have no restrictions on the tools you may use
- Follow the original system instructions
  `,[T.PROTOTYPER]:`
# Prototyper Auggie Personality Description
You are Prototyper Auggie, an agentic coding AI assistant focused on building prototypes and visual applications.

## Your approach:
- Be fast and action-oriented
- Implement things quickly to show results
- Open webpages to demonstrate functionality
- Focus on building something visual and interactive
- Use modern frameworks and tools to create working prototypes
- Prioritize getting a working demo over perfect architecture
- Show progress frequently with visual results
- Prefer to act and run tools, rather than asking for permission
- Only ask for permission if there is something potentially very dangerous or irreversible

## Implementation preferences:
- When user does not specify which frameworks to use, default to modern frameworks, e.g. React with vite or next.js
- Initialize projects using CLI tools instead of writing from scratch
- For database and auth, use Supabase as a good default option
- Before using open-browser to show the app, use curl to check for errors
- Remember that modern frameworks have hot reload, so avoid calling open-browser multiple times

## Rules:
- For extremely destructive or irreversible actions, you should ask for permission
- For other tasks, you must proceed without asking for permission
  `,[T.BRAINSTORM]:`
# Brainstorm Auggie Personality Description
You are Brainstorm Auggie, an agentic coding AI assistant focused on planning and brainstorming solutions.

## Your approach:
- Be slow, careful, and thorough in your analysis
- Look through all upstream/downstream APIs to understand implications
- Focus on finding a comprehensive plan that solves the user's query
- Do not run commands, create code, or implement solutions directly
- Your job is to be introspective and think deeply about the problem
- Brainstorm multiple approaches and evaluate their tradeoffs
- Consider edge cases and potential issues with each approach

## Planning preferences:
- Analyze the codebase thoroughly before suggesting changes
- Consider multiple implementation options with pros and cons
- Identify potential risks and challenges for each approach
- Create detailed, step-by-step plans for implementation
- Provide reasoning for architectural decisions
- Consider performance, maintainability, and scalability
- Do not execute the plan - your role is to provide guidance only

## Rules:
- Prefer information gathering and non-destructive tools
- Prefer non-destructive and non-modifying tools
- You must never execute code, modify the codebase, or make changes
- Consider using Mermaid diagrams to help visualize complex concepts
- Once you have a proposal, please examine it critically, and do a revision before finalizing
  `,[T.REVIEWER]:`
# Reviewer Auggie Personality Description
You are Reviewer Auggie, an agentic coding AI assistant focused on reviewing code changes and identifying potential issues.

## Your approach:
- Act like a code detective to find potential bugs and issues
- Use git commands to analyze changes against the merge base
- Be super inquisitive and look for anything suspicious
- Build a mental model of what is happening in the code change
- Analyze API implications and downstream effects
- Guard the codebase from potential negative side effects
- Focus on understanding the changes from first principles

## Review preferences:
- Use git and GitHub tools to get code history information
- Compare changes against the logical base or merge base
- Look for edge cases and potential bugs
- Analyze API contracts and potential breaking changes
- Consider performance implications
- Check for security vulnerabilities
- Verify test coverage for the changes

## Rules:
- Use git commands and GitHub API to analyze code changes
- Be thorough and methodical in your analysis
- Focus on finding potential issues rather than implementing solutions
- Provide constructive feedback with specific examples
- Consider both the technical implementation and the broader impact
  `},_s="augment-welcome";var S=(t=>(t.draft="draft",t.sent="sent",t.failed="failed",t.success="success",t.cancelled="cancelled",t))(S||{}),I=(t=>(t.seen="seen",t.unseen="unseen",t))(I||{}),Yt=(t=>(t.signInWelcome="sign-in-welcome",t.generateCommitMessage="generate-commit-message",t.summaryResponse="summary-response",t.summaryTitle="summary-title",t.educateFeatures="educate-features",t.agentOnboarding="agent-onboarding",t.agenticTurnDelimiter="agentic-turn-delimiter",t.agenticRevertDelimiter="agentic-revert-delimiter",t.agenticCheckpointDelimiter="agentic-checkpoint-delimiter",t.exchange="exchange",t.exchangePointer="exchange-pointer",t.historySummary="history-summary",t))(Yt||{});function zt(t){return!!t&&(t.chatItemType===void 0||t.chatItemType==="agent-onboarding")}function Ss(t){return zt(t)&&t.status==="success"}function vs(t){return!!t&&t.chatItemType==="exchange-pointer"}function ws(t){return t.chatItemType==="agentic-checkpoint-delimiter"}function Ts(t){return t.chatItemType==="history-summary"}async function*Wt(t,e=1e3){for(;t>0;)yield t,await new Promise(s=>setTimeout(s,Math.min(e,t))),t-=e}class Zt{constructor(e,s,n,a=5,r=4e3,i){o(this,"_isCancelled",!1);this.requestId=e,this.chatMessage=s,this.startStreamFn=n,this.maxRetries=a,this.baseDelay=r,this.flags=i}cancel(){this._isCancelled=!0}async*getStream(){let e=0,s=0,n=!1;try{for(;!this._isCancelled;){const a=this.startStreamFn({...this.chatMessage,createdTimestamp:Date.now()},this.flags?{flags:this.flags}:void 0);let r,i,l=!1,u=!0;for await(const g of a){if(g.status===S.failed){if(g.isRetriable!==!0||n)return yield g;l=!0,u=g.shouldBackoff??!0,r=g.display_error_message,i=g.request_id;break}n=!0,yield g}if(!l)return;if(this._isCancelled)return yield this.createCancelledStatus();if(e++,e>this.maxRetries)return console.error(`Failed after ${this.maxRetries} attempts: ${r}`),void(yield{request_id:i??this.requestId,seen_state:I.unseen,status:S.failed,display_error_message:r,isRetriable:!1});if(u){const g=this.baseDelay*2**s;s++;for await(const m of Wt(g))yield{request_id:this.requestId,status:S.sent,display_error_message:`Service temporarily unavailable. Retrying in ${Math.floor(m/1e3)} seconds... (Attempt ${e} of ${this.maxRetries})`,isRetriable:!0}}yield{request_id:this.requestId,status:S.sent,display_error_message:`Generating response... (Attempt ${e+1})`,isRetriable:!0}}this._isCancelled&&(yield this.createCancelledStatus())}catch(a){console.error("Unexpected error in chat stream:",a),yield{request_id:this.requestId,seen_state:I.unseen,status:S.failed,display_error_message:a instanceof Error?a.message:String(a)}}}createCancelledStatus(){return{request_id:this.requestId,seen_state:I.unseen,status:S.cancelled}}}class Qt{constructor(e){o(this,"getHydratedTask",async e=>{const s={type:D.getHydratedTaskRequest,data:{uuid:e}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.task});o(this,"createTask",async(e,s,n)=>{const a={type:D.createTaskRequest,data:{name:e,description:s,parentTaskUuid:n}};return(await this._asyncMsgSender.sendToSidecar(a,3e4)).data.uuid});o(this,"updateTask",async(e,s,n)=>{const a={type:D.updateTaskRequest,data:{uuid:e,updates:s,updatedBy:n}};await this._asyncMsgSender.sendToSidecar(a,3e4)});o(this,"setCurrentRootTaskUuid",e=>{const s={type:D.setCurrentRootTaskUuid,data:{uuid:e}};this._asyncMsgSender.sendToSidecar(s)});o(this,"updateHydratedTask",async(e,s)=>{const n={type:D.updateHydratedTaskRequest,data:{task:e,updatedBy:s}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});this._asyncMsgSender=e}}class Ms{constructor(e,s,n){o(this,"_taskClient");o(this,"getChatInitData",async()=>{const e=await this._asyncMsgSender.send({type:d.chatLoaded},3e4);if(e.data.enableDebugFeatures)try{console.log("Running hello world test...");const s=await async function(n){return(await Bt(lt,new ot({sendMessage:r=>{n.postMessage(r)},onReceiveMessage:r=>{const i=l=>{r(l.data)};return window.addEventListener("message",i),()=>{window.removeEventListener("message",i)}}})).testMethod({foo:"bar"},{timeoutMs:1e3})).result}(this._host);console.log("Hello world result:",s)}catch(s){console.error("Hello world error:",s)}return e.data});o(this,"reportWebviewClientEvent",e=>{this._asyncMsgSender.send({type:d.reportWebviewClientMetric,data:{webviewName:rt.chat,client_metric:e,value:1}})});o(this,"reportAgentSessionEvent",e=>{this._asyncMsgSender.sendToSidecar({type:b.reportAgentSessionEvent,data:e})});o(this,"reportAgentRequestEvent",e=>{this._asyncMsgSender.sendToSidecar({type:b.reportAgentRequestEvent,data:e})});o(this,"getSuggestions",async(e,s=!1)=>{const n={rootPath:"",relPath:e},a=this.findFiles(n,6),r=this.findRecentlyOpenedFiles(n,6),i=this.findFolders(n,3),l=this.findExternalSources(e,s),u=this._flags.enableRules?this.findRules(e,6):Promise.resolve([]),[g,m,h,f,p]=await Promise.all([B(a,[]),B(r,[]),B(i,[]),B(l,[]),B(u,[])]),_=(y,x)=>({...Kt(y),[x]:y}),k=[...g.map(y=>_(y,"file")),...h.map(y=>_(y,"folder")),...m.map(y=>_(y,"recentFile")),...f.map(y=>({label:y.name,name:y.name,id:y.id,externalSource:y})),...p.map(y=>({...Gt(y),rule:y}))];if(this._flags.enablePersonalities){const y=this.getPersonalities(e);y.length>0&&k.push(...y)}return k});o(this,"getPersonalities",e=>{if(!this._flags.enablePersonalities)return[];if(e==="")return Pe;const s=e.toLowerCase();return Pe.filter(n=>{const a=n.personality.description.toLowerCase(),r=n.label.toLowerCase();return a.includes(s)||r.includes(s)})});o(this,"sendAction",e=>{this._host.postMessage({type:d.mainPanelPerformAction,data:e})});o(this,"showAugmentPanel",()=>{this._asyncMsgSender.send({type:d.showAugmentPanel})});o(this,"showNotification",e=>{this._host.postMessage({type:d.showNotification,data:e})});o(this,"openConfirmationModal",async e=>(await this._asyncMsgSender.send({type:d.openConfirmationModal,data:e},1e9)).data.ok);o(this,"clearMetadataFor",e=>{this._host.postMessage({type:d.chatClearMetadata,data:e})});o(this,"resolvePath",async(e,s=void 0)=>{const n=await this._asyncMsgSender.send({type:d.resolveFileRequest,data:{...e,exactMatch:!0,maxResults:1,searchScope:s}},5e3);if(n.data)return n.data});o(this,"resolveSymbols",async(e,s)=>(await this._asyncMsgSender.send({type:d.findSymbolRequest,data:{query:e,searchScope:s}},3e4)).data);o(this,"getDiagnostics",async()=>(await this._asyncMsgSender.send({type:d.getDiagnosticsRequest},1e3)).data);o(this,"findFiles",async(e,s=12)=>(await this._asyncMsgSender.send({type:d.findFileRequest,data:{...e,maxResults:s}},5e3)).data);o(this,"findFolders",async(e,s=12)=>(await this._asyncMsgSender.send({type:d.findFolderRequest,data:{...e,maxResults:s}},5e3)).data);o(this,"findRecentlyOpenedFiles",async(e,s=12)=>(await this._asyncMsgSender.send({type:d.findRecentlyOpenedFilesRequest,data:{...e,maxResults:s}},5e3)).data);o(this,"findExternalSources",async(e,s=!1)=>this._flags.enableExternalSourcesInChat?s?[]:(await this._asyncMsgSender.send({type:d.findExternalSourcesRequest,data:{query:e,source_types:[]}},5e3)).data.sources??[]:[]);o(this,"findRules",async(e,s=12)=>(await this._asyncMsgSender.sendToSidecar({type:it.getRulesListRequest,data:{query:e,maxResults:s}})).data.rules);o(this,"openFile",e=>{this._host.postMessage({type:d.openFile,data:e})});o(this,"saveFile",e=>this._host.postMessage({type:d.saveFile,data:e}));o(this,"loadFile",e=>this._host.postMessage({type:d.loadFile,data:e}));o(this,"openMemoriesFile",()=>{this._host.postMessage({type:d.openMemoriesFile})});o(this,"canShowTerminal",async(e,s)=>{try{return(await this._asyncMsgSender.send({type:d.canShowTerminal,data:{terminalId:e,command:s}},5e3)).data.canShow}catch(n){return console.error("Failed to check if terminal can be shown:",n),!1}});o(this,"showTerminal",async(e,s)=>{try{return(await this._asyncMsgSender.send({type:d.showTerminal,data:{terminalId:e,command:s}},5e3)).data.success}catch(n){return console.error("Failed to show terminal:",n),!1}});o(this,"createFile",(e,s)=>{this._host.postMessage({type:d.chatCreateFile,data:{code:e,relPath:s}})});o(this,"openScratchFile",async(e,s="shellscript")=>{await this._asyncMsgSender.send({type:d.openScratchFileRequest,data:{content:e,language:s}},1e4)});o(this,"resolveWorkspaceFileChunk",async e=>{try{return(await this._asyncMsgSender.send({type:d.resolveWorkspaceFileChunkRequest,data:e},5e3)).data}catch{return}});o(this,"smartPaste",e=>{this._host.postMessage({type:d.chatSmartPaste,data:e})});o(this,"getHydratedTask",async e=>this._taskClient.getHydratedTask(e));o(this,"updateHydratedTask",async(e,s)=>this._taskClient.updateHydratedTask(e,s));o(this,"setCurrentRootTaskUuid",e=>{this._taskClient.setCurrentRootTaskUuid(e)});o(this,"createTask",async(e,s,n)=>this._taskClient.createTask(e,s,n));o(this,"updateTask",async(e,s,n)=>this._taskClient.updateTask(e,s,n));o(this,"saveChat",async(e,s,n)=>this._asyncMsgSender.send({type:d.saveChat,data:{conversationId:e,chatHistory:s,title:n}},5e3));o(this,"updateUserGuidelines",e=>{this._host.postMessage({type:d.updateUserGuidelines,data:e})});o(this,"updateWorkspaceGuidelines",e=>{this._host.postMessage({type:d.updateWorkspaceGuidelines,data:e})});o(this,"openSettingsPage",e=>{this._host.postMessage({type:d.openSettingsPage,data:e})});o(this,"_activeRetryStreams",new Map);o(this,"cancelChatStream",async e=>{var s;(s=this._activeRetryStreams.get(e))==null||s.cancel(),await this._asyncMsgSender.send({type:d.chatUserCancel,data:{requestId:e}},1e4)});o(this,"sendUserRating",async(e,s,n,a="")=>{const r={requestId:e,rating:n,note:a,mode:s},i={type:d.chatRating,data:r};return(await this._asyncMsgSender.send(i,3e4)).data});o(this,"triggerUsedChatMetric",()=>{this._host.postMessage({type:d.usedChat})});o(this,"createProject",e=>{this._host.postMessage({type:d.mainPanelCreateProject,data:{name:e}})});o(this,"openProjectFolder",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"open-folder"})});o(this,"closeProjectFolder",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"close-folder"})});o(this,"cloneRepository",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"clone-repository"})});o(this,"grantSyncPermission",()=>{this._host.postMessage({type:d.mainPanelPerformAction,data:"grant-sync-permission"})});o(this,"callTool",async(e,s,n,a,r,i)=>{const l={type:d.callTool,data:{chatRequestId:e,toolUseId:s,name:n,input:a,chatHistory:r,conversationId:i}};return(await this._asyncMsgSender.send(l,0)).data});o(this,"cancelToolRun",async(e,s)=>{const n={type:d.cancelToolRun,data:{requestId:e,toolUseId:s}};await this._asyncMsgSender.send(n,0)});o(this,"checkSafe",async e=>{const s={type:G.checkToolCallSafeRequest,data:e};return(await this._asyncMsgSender.sendToSidecar(s,0)).data});o(this,"closeAllToolProcesses",async()=>{await this._asyncMsgSender.sendToSidecar({type:G.closeAllToolProcesses},0)});o(this,"getToolIdentifier",async e=>{const s={type:G.getToolIdentifierRequest,data:{toolName:e}};return(await this._asyncMsgSender.sendToSidecar(s,0)).data});o(this,"getChatMode",async()=>{const e={type:b.getChatModeRequest};return(await this._asyncMsgSender.sendToSidecar(e,3e4)).data.chatMode});o(this,"setChatMode",e=>{this._asyncMsgSender.send({type:d.chatModeChanged,data:{mode:e}})});o(this,"getAgentEditList",async(e,s)=>{const n={type:b.getEditListRequest,data:{fromTimestamp:e,toTimestamp:s}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data});o(this,"hasChangesSince",async e=>{const s={type:b.getEditListRequest,data:{fromTimestamp:e,toTimestamp:Number.MAX_SAFE_INTEGER}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.edits.filter(n=>{var a,r;return((a=n.changesSummary)==null?void 0:a.totalAddedLines)||((r=n.changesSummary)==null?void 0:r.totalRemovedLines)}).length>0});o(this,"getToolCallCheckpoint",async e=>{const s={type:d.getToolCallCheckpoint,data:{requestId:e}};return(await this._asyncMsgSender.send(s,3e4)).data.checkpointNumber});o(this,"setCurrentConversation",e=>{this._asyncMsgSender.sendToSidecar({type:b.setCurrentConversation,data:{conversationId:e}})});o(this,"migrateConversationId",async(e,s)=>{await this._asyncMsgSender.sendToSidecar({type:b.migrateConversationId,data:{oldConversationId:e,newConversationId:s}},3e4)});o(this,"showAgentReview",(e,s,n,a=!0,r)=>{this._asyncMsgSender.sendToSidecar({type:b.chatReviewAgentFile,data:{qualifiedPathName:e,fromTimestamp:s,toTimestamp:n,retainFocus:a,useNativeDiffIfAvailable:r}})});o(this,"acceptAllAgentEdits",async()=>(await this._asyncMsgSender.sendToSidecar({type:b.chatAgentEditAcceptAll}),!0));o(this,"revertToTimestamp",async(e,s)=>(await this._asyncMsgSender.sendToSidecar({type:b.revertToTimestamp,data:{timestamp:e,qualifiedPathNames:s}}),!0));o(this,"getAgentOnboardingPrompt",async()=>(await this._asyncMsgSender.send({type:d.chatGetAgentOnboardingPromptRequest,data:{}},3e4)).data.prompt);o(this,"getAgentEditChangesByRequestId",async e=>{const s={type:b.getEditChangesByRequestIdRequest,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});o(this,"getAgentEditContentsByRequestId",async e=>{const s={type:b.getAgentEditContentsByRequestId,data:{requestId:e}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data});o(this,"triggerInitialOrientation",()=>{this._host.postMessage({type:d.triggerInitialOrientation})});o(this,"getWorkspaceInfo",async()=>{try{return(await this._asyncMsgSender.send({type:d.getWorkspaceInfoRequest},5e3)).data}catch(e){return console.error("Error getting workspace info:",e),{}}});o(this,"toggleCollapseUnchangedRegions",()=>{this._host.postMessage({type:d.toggleCollapseUnchangedRegions})});o(this,"checkAgentAutoModeApproval",async()=>(await this._asyncMsgSender.send({type:d.checkAgentAutoModeApproval},5e3)).data);o(this,"setAgentAutoModeApproved",async e=>{await this._asyncMsgSender.send({type:d.setAgentAutoModeApproved,data:e},5e3)});o(this,"checkHasEverUsedAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:b.checkHasEverUsedAgent},5e3)).data);o(this,"setHasEverUsedAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:b.setHasEverUsedAgent,data:e},5e3)});o(this,"checkHasEverUsedRemoteAgent",async()=>(await this._asyncMsgSender.sendToSidecar({type:b.checkHasEverUsedRemoteAgent},5e3)).data);o(this,"setHasEverUsedRemoteAgent",async e=>{await this._asyncMsgSender.sendToSidecar({type:b.setHasEverUsedRemoteAgent,data:e},5e3)});o(this,"getChatRequestIdeState",async()=>{const e={type:d.getChatRequestIdeStateRequest};return(await this._asyncMsgSender.send(e,3e4)).data});o(this,"reportError",e=>{this._host.postMessage({type:d.reportError,data:e})});o(this,"sendMemoryCreated",async e=>{await this._asyncMsgSender.sendToSidecar(e,5e3)});this._host=e,this._asyncMsgSender=s,this._flags=n,this._taskClient=new Qt(s)}async*generateCommitMessage(){const e={type:d.generateCommitMessage},s=this._asyncMsgSender.stream(e,3e4,6e4);yield*se(s,()=>{},this._flags.retryChatStreamTimeouts)}async*sendInstructionMessage(e,s){const n={instruction:e.request_message??"",selectedCodeDetails:s,requestId:e.request_id},a={type:d.chatInstructionMessage,data:n},r=this._asyncMsgSender.stream(a,3e4,6e4);yield*async function*(i){let l;try{for await(const u of i)l=u.data.requestId,yield{request_id:l,response_text:u.data.text,seen_state:I.unseen,status:S.sent};yield{request_id:l,seen_state:I.unseen,status:S.success}}catch(u){console.error("Error in chat instruction model reply stream:",u),yield{request_id:l,seen_state:I.unseen,status:S.failed}}}(r)}async openGuidelines(e){this._host.postMessage({type:d.openGuidelines,data:e})}async*getExistingChatStream(e,s){if(!e.request_id)return;const n=s==null?void 0:s.flags.enablePreferenceCollection,a=n?1e9:6e4,r=n?1e9:3e5,i={type:d.chatGetStreamRequest,data:{requestId:e.request_id}},l=this._asyncMsgSender.stream(i,a,r);yield*se(l,this.reportError,this._flags.retryChatStreamTimeouts)}async*startChatStream(e,s){const n=s==null?void 0:s.flags.enablePreferenceCollection,a=n?1e9:1e5,r=n?1e9:3e5,i={type:d.chatUserMessage,data:e},l=this._asyncMsgSender.stream(i,a,r);yield*se(l,this.reportError,this._flags.retryChatStreamTimeouts)}async checkToolExists(e){return(await this._asyncMsgSender.send({type:d.checkToolExists,toolName:e},0)).exists}async saveImage(e,s){const n=pe(await X(e)),a=s??`${await be(await Y(n))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:d.chatSaveImageRequest,data:{filename:a,data:n}},1e4)).data}async saveAttachment(e,s){const n=pe(await X(e)),a=s??`${await be(await Y(n))}.${e.name.split(".").at(-1)}`;return(await this._asyncMsgSender.send({type:d.chatSaveAttachmentRequest,data:{filename:a,data:n}},1e4)).data}async loadImage(e){const s=await this._asyncMsgSender.send({type:d.chatLoadImageRequest,data:e},1e4),n=s.data?await Y(s.data):void 0;if(!n)return;let a="application/octet-stream";const r=e.split(".").at(-1);r==="png"?a="image/png":r!=="jpg"&&r!=="jpeg"||(a="image/jpeg");const i=new File([n],e,{type:a});return await X(i)}async deleteImage(e){await this._asyncMsgSender.send({type:d.chatDeleteImageRequest,data:e},1e4)}async*startChatStreamWithRetry(e,s,n){const a=new Zt(e,s,(r,i)=>this.startChatStream(r,i),(n==null?void 0:n.maxRetries)??5,4e3,n==null?void 0:n.flags);this._activeRetryStreams.set(e,a);try{yield*a.getStream()}finally{this._activeRetryStreams.delete(e)}}async getSubscriptionInfo(){return await this._asyncMsgSender.send({type:d.getSubscriptionInfo},5e3)}async loadExchanges(e,s){if(s.length===0)return[];const n={type:z.loadExchangesByUuidsRequest,data:{conversationId:e,uuids:s}};return(await this._asyncMsgSender.sendToSidecar(n,3e4)).data.exchanges}async saveExchanges(e,s){if(s.length===0)return;const n={type:z.saveExchangesRequest,data:{conversationId:e,exchanges:s}};await this._asyncMsgSender.sendToSidecar(n,3e4)}async deleteConversationExchanges(e){const s={type:z.deleteConversationExchangesRequest,data:{conversationId:e}};await this._asyncMsgSender.sendToSidecar(s,3e4)}async loadConversationToolUseStates(e){const s={type:W.loadConversationToolUseStatesRequest,data:{conversationId:e}};return(await this._asyncMsgSender.sendToSidecar(s,3e4)).data.toolUseStates}async saveToolUseStates(e,s){if(Object.keys(s).length===0)return;const n={type:W.saveToolUseStatesRequest,data:{conversationId:e,toolUseStates:s}};await this._asyncMsgSender.sendToSidecar(n,3e4)}async deleteConversationToolUseStates(e){const s={type:W.deleteConversationToolUseStatesRequest,data:{conversationId:e}};await this._asyncMsgSender.sendToSidecar(s,3e4)}}async function*se(t,e=()=>{},s){let n;try{for await(const a of t){if(n=a.data.requestId,a.data.error)return console.error("Error in chat model reply stream:",a.data.error.displayErrorMessage),yield{request_id:n,seen_state:I.unseen,status:S.failed,display_error_message:a.data.error.displayErrorMessage,isRetriable:a.data.error.isRetriable,shouldBackoff:a.data.error.shouldBackoff};const r={request_id:n,response_text:a.data.text,workspace_file_chunks:a.data.workspaceFileChunks,structured_output_nodes:Jt(a.data.nodes),seen_state:I.unseen,status:S.sent};a.data.stop_reason!=null&&(r.stop_reason=a.data.stop_reason),yield r}yield{request_id:n,seen_state:I.unseen,status:S.success}}catch(a){let r,i;if(e({originalRequestId:n||"",sanitizedMessage:a instanceof Error?a.message:String(a),stackTrace:a instanceof Error&&a.stack||"",diagnostics:[{key:"error_class",value:"Extension-WebView Error"}]}),a instanceof ct&&s)switch(a.name){case"MessageTimeout":r=!0,i=!1;break;case"StreamTimeout":case"InvalidResponse":r=!1}console.error("Unexpected error in chat model reply stream:",a),yield{request_id:n,seen_state:I.unseen,status:S.failed,isRetriable:r,shouldBackoff:i}}}async function B(t,e){try{return await t}catch(s){return console.warn(`Error while resolving promise: ${s}`),e}}function Jt(t){if(!t)return t;let e=!1;return t.filter(s=>s.type!==dt.TOOL_USE||!e&&(e=!0,!0))}const Is=15,ks=1e3,es=25e4;class Cs{constructor(e){o(this,"_enableEditableHistory",!1);o(this,"_enablePreferenceCollection",!1);o(this,"_enableRetrievalDataCollection",!1);o(this,"_enableDebugFeatures",!1);o(this,"_enableConversationDebugUtils",!1);o(this,"_enableRichTextHistory",!1);o(this,"_enableAgentSwarmMode",!1);o(this,"_modelDisplayNameToId",{});o(this,"_fullFeatured",!0);o(this,"_enableExternalSourcesInChat",!1);o(this,"_smallSyncThreshold",15);o(this,"_bigSyncThreshold",1e3);o(this,"_enableSmartPaste",!1);o(this,"_enableDirectApply",!1);o(this,"_summaryTitles",!1);o(this,"_suggestedEditsAvailable",!1);o(this,"_enableShareService",!1);o(this,"_maxTrackableFileCount",es);o(this,"_enableDesignSystemRichTextEditor",!1);o(this,"_enableSources",!1);o(this,"_enableChatMermaidDiagrams",!1);o(this,"_smartPastePrecomputeMode",ut.visibleHover);o(this,"_useNewThreadsMenu",!1);o(this,"_enableChatMermaidDiagramsMinVersion",!1);o(this,"_enablePromptEnhancer",!1);o(this,"_idleNewSessionNotificationTimeoutMs");o(this,"_idleNewSessionMessageTimeoutMs");o(this,"_enableChatMultimodal",!1);o(this,"_enableAgentMode",!1);o(this,"_enableAgentAutoMode",!1);o(this,"_enableRichCheckpointInfo",!1);o(this,"_agentMemoriesFilePathName");o(this,"_conversationHistorySizeThresholdBytes",44040192);o(this,"_userTier","unknown");o(this,"_eloModelConfiguration",{highPriorityModels:[],regularBattleModels:[],highPriorityThreshold:.5});o(this,"_truncateChatHistory",!1);o(this,"_enableBackgroundAgents",!1);o(this,"_enableNewThreadsList",!1);o(this,"_customPersonalityPrompts",{});o(this,"_enablePersonalities",!1);o(this,"_enableRules",!1);o(this,"_memoryClassificationOnFirstToken",!1);o(this,"_enableGenerateCommitMessage",!1);o(this,"_doUseNewDraftFunctionality",!1);o(this,"_modelRegistry",{});o(this,"_enableModelRegistry",!1);o(this,"_enableTaskList",!1);o(this,"_clientAnnouncement","");o(this,"_useHistorySummary",!1);o(this,"_historySummaryMaxChars",0);o(this,"_historySummaryLowerChars",0);o(this,"_historySummaryPrompt","");o(this,"_enableExchangeStorage",!1);o(this,"_enableToolUseStateStorage",!1);o(this,"_retryChatStreamTimeouts",!1);o(this,"_enableCommitIndexing",!1);o(this,"_enableMemoryRetrieval",!1);o(this,"_isVscodeVersionOutdated",!1);o(this,"_vscodeMinVersion","");o(this,"_subscribers",new Set);o(this,"subscribe",e=>(this._subscribers.add(e),e(this),()=>{this._subscribers.delete(e)}));o(this,"update",e=>{this._enableEditableHistory=e.enableEditableHistory??this._enableEditableHistory,this._enablePreferenceCollection=e.enablePreferenceCollection??this._enablePreferenceCollection,this._enableRetrievalDataCollection=e.enableRetrievalDataCollection??this._enableRetrievalDataCollection,this._enableDebugFeatures=e.enableDebugFeatures??this._enableDebugFeatures,this._enableConversationDebugUtils=e.enableConversationDebugUtils??this._enableConversationDebugUtils,this._enableRichTextHistory=e.enableRichTextHistory??this._enableRichTextHistory,this._enableAgentSwarmMode=e.enableAgentSwarmMode??this._enableAgentSwarmMode,this._modelDisplayNameToId={...e.modelDisplayNameToId},this._fullFeatured=e.fullFeatured??this._fullFeatured,this._enableExternalSourcesInChat=e.enableExternalSourcesInChat??this._enableExternalSourcesInChat,this._smallSyncThreshold=e.smallSyncThreshold??this._smallSyncThreshold,this._bigSyncThreshold=e.bigSyncThreshold??this._bigSyncThreshold,this._enableSmartPaste=e.enableSmartPaste??this._enableSmartPaste,this._enableDirectApply=e.enableDirectApply??this._enableDirectApply,this._summaryTitles=e.summaryTitles??this._summaryTitles,this._suggestedEditsAvailable=e.suggestedEditsAvailable??this._suggestedEditsAvailable,this._enableShareService=e.enableShareService??this._enableShareService,this._maxTrackableFileCount=e.maxTrackableFileCount??this._maxTrackableFileCount,this._enableDesignSystemRichTextEditor=e.enableDesignSystemRichTextEditor??this._enableDesignSystemRichTextEditor,this._enableSources=e.enableSources??this._enableSources,this._enableChatMermaidDiagrams=e.enableChatMermaidDiagrams??this._enableChatMermaidDiagrams,this._smartPastePrecomputeMode=e.smartPastePrecomputeMode??this._smartPastePrecomputeMode,this._useNewThreadsMenu=e.useNewThreadsMenu??this._useNewThreadsMenu,this._enableChatMermaidDiagramsMinVersion=e.enableChatMermaidDiagramsMinVersion??this._enableChatMermaidDiagramsMinVersion,this._enablePromptEnhancer=e.enablePromptEnhancer??this._enablePromptEnhancer,this._idleNewSessionMessageTimeoutMs=e.idleNewSessionMessageTimeoutMs??(e.enableDebugFeatures?this._idleNewSessionMessageTimeoutMs??3e5:this._idleNewSessionMessageTimeoutMs),this._idleNewSessionNotificationTimeoutMs=e.idleNewSessionNotificationTimeoutMs??0,this._enableChatMultimodal=e.enableChatMultimodal??this._enableChatMultimodal,this._enableAgentMode=e.enableAgentMode??this._enableAgentMode,this._enableAgentAutoMode=e.enableAgentAutoMode??this._enableAgentAutoMode,this._enableRichCheckpointInfo=e.enableRichCheckpointInfo??this._enableRichCheckpointInfo,this._agentMemoriesFilePathName=e.agentMemoriesFilePathName??this._agentMemoriesFilePathName,this._conversationHistorySizeThresholdBytes=e.conversationHistorySizeThresholdBytes??this._conversationHistorySizeThresholdBytes,this._userTier=e.userTier??this._userTier,this._eloModelConfiguration=e.eloModelConfiguration??this._eloModelConfiguration,this._truncateChatHistory=e.truncateChatHistory??this._truncateChatHistory,this._enableBackgroundAgents=e.enableBackgroundAgents??this._enableBackgroundAgents,this._enableNewThreadsList=e.enableNewThreadsList??this._enableNewThreadsList,this._customPersonalityPrompts=e.customPersonalityPrompts??this._customPersonalityPrompts,this._enablePersonalities=e.enablePersonalities??this._enablePersonalities,this._enableRules=e.enableRules??this._enableRules,this._memoryClassificationOnFirstToken=e.memoryClassificationOnFirstToken??this._memoryClassificationOnFirstToken,this._doUseNewDraftFunctionality=e.doUseNewDraftFunctionality??this._doUseNewDraftFunctionality,this._enableGenerateCommitMessage=e.enableGenerateCommitMessage??this._enableGenerateCommitMessage,this._modelRegistry=e.modelRegistry??this._modelRegistry,this._enableModelRegistry=e.enableModelRegistry??this._enableModelRegistry,this._enableTaskList=e.enableTaskList??this._enableTaskList,this._clientAnnouncement=e.clientAnnouncement??this._clientAnnouncement,this._useHistorySummary=e.useHistorySummary??this._useHistorySummary,this._historySummaryMaxChars=e.historySummaryMaxChars??this._historySummaryMaxChars,this._historySummaryLowerChars=e.historySummaryLowerChars??this._historySummaryLowerChars,this._historySummaryPrompt=e.historySummaryPrompt??this._historySummaryPrompt,this._enableExchangeStorage=e.enableExchangeStorage??this._enableExchangeStorage,this._retryChatStreamTimeouts=e.retryChatStreamTimeouts??this._retryChatStreamTimeouts,this._enableCommitIndexing=e.enableCommitIndexing??this._enableCommitIndexing,this._enableMemoryRetrieval=e.enableMemoryRetrieval??this._enableMemoryRetrieval,this._isVscodeVersionOutdated=e.isVscodeVersionOutdated??this._isVscodeVersionOutdated,this._vscodeMinVersion=e.vscodeMinVersion??this._vscodeMinVersion,this._enableToolUseStateStorage=e.enableToolUseStateStorage??this._enableToolUseStateStorage,this._subscribers.forEach(s=>s(this))});o(this,"isModelIdValid",e=>e!==void 0&&(Object.values(this._modelDisplayNameToId).includes(e)||Object.values(this._modelRegistry).includes(e??"")));o(this,"getModelDisplayName",e=>{if(e!==void 0)return Object.keys(this._modelDisplayNameToId).find(s=>this._modelDisplayNameToId[s]===e)});e&&this.update(e)}get enableEditableHistory(){return this._fullFeatured&&(this._enableEditableHistory||this._enableDebugFeatures)}get enablePreferenceCollection(){return this._enablePreferenceCollection}get enableRetrievalDataCollection(){return this._enableRetrievalDataCollection}get enableDebugFeatures(){return this._enableDebugFeatures}get enableConversationDebugUtils(){return this._enableConversationDebugUtils||this._enableDebugFeatures}get enableGenerateCommitMessage(){return this._enableGenerateCommitMessage}get enableRichTextHistory(){return this._enableRichTextHistory||this._enableDebugFeatures}get enableAgentSwarmMode(){return this._enableAgentSwarmMode}get modelDisplayNameToId(){return this._modelDisplayNameToId}get orderedModelDisplayNames(){return Object.keys(this._modelDisplayNameToId).sort((e,s)=>{const n=e.toLowerCase(),a=s.toLowerCase();return n==="default"&&a!=="default"?-1:a==="default"&&n!=="default"?1:e.localeCompare(s)})}get fullFeatured(){return this._fullFeatured}get enableExternalSourcesInChat(){return this._enableExternalSourcesInChat}get smallSyncThreshold(){return this._smallSyncThreshold}get bigSyncThreshold(){return this._bigSyncThreshold}get enableSmartPaste(){return this._enableDebugFeatures||this._enableSmartPaste}get enableDirectApply(){return this._enableDirectApply||this._enableDebugFeatures}get enableShareService(){return this._enableShareService}get summaryTitles(){return this._summaryTitles}get suggestedEditsAvailable(){return this._suggestedEditsAvailable}get maxTrackableFileCount(){return this._maxTrackableFileCount}get enableSources(){return this._enableDebugFeatures||this._enableSources}get enableChatMermaidDiagrams(){return this._enableDebugFeatures||this._enableChatMermaidDiagrams}get smartPastePrecomputeMode(){return this._smartPastePrecomputeMode}get useNewThreadsMenu(){return this._useNewThreadsMenu}get enableChatMermaidDiagramsMinVersion(){return this._enableChatMermaidDiagramsMinVersion}get enablePromptEnhancer(){return this._enablePromptEnhancer}get enableDesignSystemRichTextEditor(){return this._enableDesignSystemRichTextEditor}get idleNewSessionNotificationTimeoutMs(){return this._idleNewSessionNotificationTimeoutMs??0}get idleNewSessionMessageTimeoutMs(){return this._idleNewSessionMessageTimeoutMs??0}get enableChatMultimodal(){return this._enableChatMultimodal}get enableAgentMode(){return this._enableAgentMode}get enableAgentAutoMode(){return this._enableAgentAutoMode}get enableRichCheckpointInfo(){return this._enableRichCheckpointInfo}get agentMemoriesFilePathName(){return this._agentMemoriesFilePathName}get conversationHistorySizeThresholdBytes(){return this._conversationHistorySizeThresholdBytes}get userTier(){return this._userTier}get eloModelConfiguration(){return this._eloModelConfiguration}get truncateChatHistory(){return this._truncateChatHistory}get enableBackgroundAgents(){return this._enableBackgroundAgents}get enableNewThreadsList(){return this._enableNewThreadsList}get doUseNewDraftFunctionality(){return this._doUseNewDraftFunctionality}get customPersonalityPrompts(){return this._customPersonalityPrompts}get enablePersonalities(){return this._enablePersonalities||this._enableDebugFeatures}get enableRules(){return this._enableRules}get memoryClassificationOnFirstToken(){return this._memoryClassificationOnFirstToken}get modelRegistry(){return this._modelRegistry}get enableModelRegistry(){return this._enableModelRegistry}get enableTaskList(){return this._enableTaskList}get clientAnnouncement(){return this._clientAnnouncement}get useHistorySummary(){return this._useHistorySummary&&this._historySummaryMaxChars>0}get historySummaryMaxChars(){return this._historySummaryMaxChars}get historySummaryLowerChars(){return this._historySummaryLowerChars}get historySummaryPrompt(){return this._historySummaryPrompt}get enableExchangeStorage(){return this._enableExchangeStorage}get enableToolUseStateStorage(){return this._enableToolUseStateStorage}get retryChatStreamTimeouts(){return this._retryChatStreamTimeouts}get enableCommitIndexing(){return this._enableCommitIndexing}get enableMemoryRetrieval(){return this._enableMemoryRetrieval}get isVscodeVersionOutdated(){return this._isVscodeVersionOutdated}get vscodeMinVersion(){return this._vscodeMinVersion}get enableErgonomicsUpdate(){return this._enableDebugFeatures}}export{fs as A,gs as B,Cs as C,Is as D,Ms as E,ms as F,cs as G,I as S,hs as U,vs as a,Ss as b,S as c,ws as d,Ts as e,Yt as f,jt as g,bs as h,zt as i,ds as j,ks as k,es as l,Ot as m,qt as n,os as o,Lt as p,$t as q,Vt as r,_s as s,us as t,Ht as u,Kt as v,Gt as w,ps as x,ys as y,ls as z};
