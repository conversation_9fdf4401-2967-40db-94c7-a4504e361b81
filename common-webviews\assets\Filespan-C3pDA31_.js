import{S as A,i as Z,s as b,X as k,E as q,F as B,u as _,t as j,G as C,J as F,D as y,V as z,Y as S,c as v,a9 as M,e as V,f,K as J,L as K,M as L,Z as X,h as Y}from"./SpinnerAugment-CL9SZpf8.js";import{n as H,g as N,a as O}from"./file-paths-BPg3etNg.js";const P=n=>({}),w=n=>({}),Q=n=>({}),D=n=>({}),R=n=>({}),E=n=>({});function G(n){let e,c,l;return{c(){e=y("div"),c=y("div"),l=S(n[3]),v(c,"class","c-filespan__dir-text svelte-juudge"),v(e,"class","c-filespan__dir svelte-juudge")},m(a,o){V(a,e,o),f(e,c),f(c,l)},p(a,o){8&o&&X(l,a[3])},d(a){a&&Y(e)}}}function T(n){let e,c,l,a,o,x,h,m,i;const g=n[7].leftIcon,s=F(g,n,n[8],E),p=n[7].middleIcon,u=F(p,n,n[8],D);let $=!n[2]&&G(n);const I=n[7].rightIcon,d=F(I,n,n[8],w);return{c(){e=y("div"),s&&s.c(),c=z(),l=y("span"),a=S(n[4]),o=z(),u&&u.c(),x=z(),$&&$.c(),h=z(),d&&d.c(),v(l,"class","c-filespan__filename svelte-juudge"),v(e,"class",m=M(`c-filespan ${n[0]}`)+" svelte-juudge")},m(t,r){V(t,e,r),s&&s.m(e,null),f(e,c),f(e,l),f(l,a),f(e,o),u&&u.m(e,null),f(e,x),$&&$.m(e,null),f(e,h),d&&d.m(e,null),i=!0},p(t,r){s&&s.p&&(!i||256&r)&&J(s,g,t,t[8],i?L(g,t[8],r,R):K(t[8]),E),(!i||16&r)&&X(a,t[4]),u&&u.p&&(!i||256&r)&&J(u,p,t,t[8],i?L(p,t[8],r,Q):K(t[8]),D),t[2]?$&&($.d(1),$=null):$?$.p(t,r):($=G(t),$.c(),$.m(e,h)),d&&d.p&&(!i||256&r)&&J(d,I,t,t[8],i?L(I,t[8],r,P):K(t[8]),w),(!i||1&r&&m!==(m=M(`c-filespan ${t[0]}`)+" svelte-juudge"))&&v(e,"class",m)},i(t){i||(_(s,t),_(u,t),_(d,t),i=!0)},o(t){j(s,t),j(u,t),j(d,t),i=!1},d(t){t&&Y(e),s&&s.d(t),u&&u.d(t),$&&$.d(),d&&d.d(t)}}}function U(n){let e,c;return e=new k({props:{size:n[1],$$slots:{default:[T]},$$scope:{ctx:n}}}),{c(){q(e.$$.fragment)},m(l,a){B(e,l,a),c=!0},p(l,[a]){const o={};2&a&&(o.size=l[1]),285&a&&(o.$$scope={dirty:a,ctx:l}),e.$set(o)},i(l){c||(_(e.$$.fragment,l),c=!0)},o(l){j(e.$$.fragment,l),c=!1},d(l){C(e,l)}}}function W(n,e,c){let l,a,o,{$$slots:x={},$$scope:h}=e,{class:m=""}=e,{filepath:i}=e,{size:g=1}=e,{nopath:s=!1}=e;return n.$$set=p=>{"class"in p&&c(0,m=p.class),"filepath"in p&&c(5,i=p.filepath),"size"in p&&c(1,g=p.size),"nopath"in p&&c(2,s=p.nopath),"$$scope"in p&&c(8,h=p.$$scope)},n.$$.update=()=>{32&n.$$.dirty&&c(6,l=H(i)),64&n.$$.dirty&&c(4,a=N(l)),64&n.$$.dirty&&c(3,o=O(l))},[m,g,s,o,a,i,l,x,h]}class ne extends A{constructor(e){super(),Z(this,e,W,U,b,{class:0,filepath:5,size:1,nopath:2})}}export{ne as F};
