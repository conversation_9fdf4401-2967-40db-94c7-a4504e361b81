var $i=Object.defineProperty;var Ii=(t,e,n)=>e in t?$i(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n;var Y=(t,e,n)=>Ii(t,typeof e!="symbol"?e+"":e,n);import{S as Me,i as je,s as Fe,b as at,c as B,e as me,f as Ce,n as ie,h as J,a as Z,H as vn,w as yn,x as bn,y as Sn,d as Pe,z as wn,g as mt,j as we,J as Dt,a0 as Mr,ar as jr,D as Ee,V as St,a1 as Fr,a2 as X,Q as fe,K as Lt,L as Nt,M as Mt,u as K,q as On,t as re,r as Rn,T as go,I as Ci,al as Pi,X as Ai,E as _o,F as vo,G as yo,W as H,a9 as Oi,ad as Ri,a3 as qr,ai as Di,Y as Br,Z as zr,A as Li,an as Dn}from"./SpinnerAugment-CL9SZpf8.js";import{C as Ni}from"./IconButtonAugment-C4xMcLhX.js";import{o as Ln}from"./index-BAWb-tvr.js";const $=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Je="9.20.0",N=globalThis;function nt(){return En(N),N}function En(t){const e=t.__SENTRY__=t.__SENTRY__||{};return e.version=e.version||Je,e[Je]=e[Je]||{}}function cn(t,e,n=N){const r=n.__SENTRY__=n.__SENTRY__||{},s=r[Je]=r[Je]||{};return s[t]||(s[t]=e())}const bo=Object.prototype.toString;function Er(t){switch(bo.call(t)){case"[object Error]":case"[object Exception]":case"[object DOMException]":case"[object WebAssembly.Exception]":return!0;default:return xe(t,Error)}}function gt(t,e){return bo.call(t)===`[object ${e}]`}function So(t){return gt(t,"ErrorEvent")}function Ur(t){return gt(t,"DOMError")}function Se(t){return gt(t,"String")}function xr(t){return typeof t=="object"&&t!==null&&"__sentry_template_string__"in t&&"__sentry_template_values__"in t}function Tt(t){return t===null||xr(t)||typeof t!="object"&&typeof t!="function"}function kt(t){return gt(t,"Object")}function xn(t){return typeof Event<"u"&&xe(t,Event)}function Tn(t){return!!(t!=null&&t.then&&typeof t.then=="function")}function xe(t,e){try{return t instanceof e}catch{return!1}}function wo(t){return!(typeof t!="object"||t===null||!t.__isVue&&!t._isVue)}function Eo(t){return typeof Request<"u"&&xe(t,Request)}const Tr=N,Mi=80;function Ze(t,e={}){if(!t)return"<unknown>";try{let n=t;const r=5,s=[];let o=0,i=0;const a=" > ",c=a.length;let u;const l=Array.isArray(e)?e:e.keyAttrs,h=!Array.isArray(e)&&e.maxStringLength||Mi;for(;n&&o++<r&&(u=ji(n,l),!(u==="html"||o>1&&i+s.length*c+u.length>=h));)s.push(u),i+=u.length,n=n.parentNode;return s.reverse().join(a)}catch{return"<unknown>"}}function ji(t,e){const n=t,r=[];if(!(n!=null&&n.tagName))return"";if(Tr.HTMLElement&&n instanceof HTMLElement&&n.dataset){if(n.dataset.sentryComponent)return n.dataset.sentryComponent;if(n.dataset.sentryElement)return n.dataset.sentryElement}r.push(n.tagName.toLowerCase());const s=e!=null&&e.length?e.filter(i=>n.getAttribute(i)).map(i=>[i,n.getAttribute(i)]):null;if(s!=null&&s.length)s.forEach(i=>{r.push(`[${i[0]}="${i[1]}"]`)});else{n.id&&r.push(`#${n.id}`);const i=n.className;if(i&&Se(i)){const a=i.split(/\s+/);for(const c of a)r.push(`.${c}`)}}const o=["aria-label","type","name","title","alt"];for(const i of o){const a=n.getAttribute(i);a&&r.push(`[${i}="${a}"]`)}return r.join("")}function jt(){try{return Tr.document.location.href}catch{return""}}function xo(t){if(!Tr.HTMLElement)return null;let e=t;for(let n=0;n<5;n++){if(!e)return null;if(e instanceof HTMLElement){if(e.dataset.sentryComponent)return e.dataset.sentryComponent;if(e.dataset.sentryElement)return e.dataset.sentryElement}e=e.parentNode}return null}const nr=["debug","info","warn","error","log","assert","trace"],un={};function rt(t){if(!("console"in N))return t();const e=N.console,n={},r=Object.keys(un);r.forEach(s=>{const o=un[s];n[s]=e[s],e[s]=o});try{return t()}finally{r.forEach(s=>{e[s]=n[s]})}}const w=cn("logger",function(){let t=!1;const e={enable:()=>{t=!0},disable:()=>{t=!1},isEnabled:()=>t};return $?nr.forEach(n=>{e[n]=(...r)=>{t&&rt(()=>{N.console[n](`Sentry Logger [${n}]:`,...r)})}}):nr.forEach(n=>{e[n]=()=>{}}),e});function dn(t,e=0){return typeof t!="string"||e===0||t.length<=e?t:`${t.slice(0,e)}...`}function Hr(t,e){if(!Array.isArray(t))return"";const n=[];for(let r=0;r<t.length;r++){const s=t[r];try{wo(s)?n.push("[VueViewModel]"):n.push(String(s))}catch{n.push("[value cannot be serialized]")}}return n.join(e)}function Fi(t,e,n=!1){return!!Se(t)&&(gt(e,"RegExp")?e.test(t):!!Se(e)&&(n?t===e:t.includes(e)))}function We(t,e=[],n=!1){return e.some(r=>Fi(t,r,n))}function oe(t,e,n){if(!(e in t))return;const r=t[e];if(typeof r!="function")return;const s=n(r);typeof s=="function"&&To(s,r);try{t[e]=s}catch{$&&w.log(`Failed to replace method "${e}" in object`,t)}}function ae(t,e,n){try{Object.defineProperty(t,e,{value:n,writable:!0,configurable:!0})}catch{$&&w.log(`Failed to add non-enumerable property "${e}" to object`,t)}}function To(t,e){try{const n=e.prototype||{};t.prototype=e.prototype=n,ae(t,"__sentry_original__",e)}catch{}}function kr(t){return t.__sentry_original__}function ko(t){if(Er(t))return{message:t.message,name:t.name,stack:t.stack,...Jr(t)};if(xn(t)){const e={type:t.type,target:Wr(t.target),currentTarget:Wr(t.currentTarget),...Jr(t)};return typeof CustomEvent<"u"&&xe(t,CustomEvent)&&(e.detail=t.detail),e}return t}function Wr(t){try{return e=t,typeof Element<"u"&&xe(e,Element)?Ze(t):Object.prototype.toString.call(t)}catch{return"<unknown>"}var e}function Jr(t){if(typeof t=="object"&&t!==null){const e={};for(const n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e}return{}}function de(t=function(){const e=N;return e.crypto||e.msCrypto}()){let e=()=>16*Math.random();try{if(t!=null&&t.randomUUID)return t.randomUUID().replace(/-/g,"");t!=null&&t.getRandomValues&&(e=()=>{const n=new Uint8Array(1);return t.getRandomValues(n),n[0]})}catch{}return("10000000100040008000"+1e11).replace(/[018]/g,n=>(n^(15&e())>>n/4).toString(16))}function $o(t){var e,n;return(n=(e=t.exception)==null?void 0:e.values)==null?void 0:n[0]}function Ue(t){const{message:e,event_id:n}=t;if(e)return e;const r=$o(t);return r?r.type&&r.value?`${r.type}: ${r.value}`:r.type||r.value||n||"<unknown>":n||"<unknown>"}function rr(t,e,n){const r=t.exception=t.exception||{},s=r.values=r.values||[],o=s[0]=s[0]||{};o.value||(o.value=e||""),o.type||(o.type="Error")}function ct(t,e){const n=$o(t);if(!n)return;const r=n.mechanism;if(n.mechanism={type:"generic",handled:!0,...r,...e},e&&"data"in e){const s={...r==null?void 0:r.data,...e.data};n.mechanism.data=s}}function Gr(t){if(function(e){try{return e.__sentry_captured__}catch{}}(t))return!0;try{ae(t,"__sentry_captured__",!0)}catch{}return!1}const Io=1e3;function Ft(){return Date.now()/Io}const te=function(){const{performance:t}=N;if(!(t!=null&&t.now))return Ft;const e=Date.now()-t.now(),n=t.timeOrigin==null?e:t.timeOrigin;return()=>(n+t.now())/Io}();let Nn;function ue(){return Nn||(Nn=function(){var c;const{performance:t}=N;if(!(t!=null&&t.now))return[void 0,"none"];const e=36e5,n=t.now(),r=Date.now(),s=t.timeOrigin?Math.abs(t.timeOrigin+n-r):e,o=s<e,i=(c=t.timing)==null?void 0:c.navigationStart,a=typeof i=="number"?Math.abs(i+n-r):e;return o||a<e?s<=a?[t.timeOrigin,"timeOrigin"]:[i,"navigationStart"]:[r,"dateNow"]}()),Nn[0]}function qi(t){const e=te(),n={sid:de(),init:!0,timestamp:e,started:e,duration:0,status:"ok",errors:0,ignoreDuration:!1,toJSON:()=>function(r){return{sid:`${r.sid}`,init:r.init,started:new Date(1e3*r.started).toISOString(),timestamp:new Date(1e3*r.timestamp).toISOString(),status:r.status,errors:r.errors,did:typeof r.did=="number"||typeof r.did=="string"?`${r.did}`:void 0,duration:r.duration,abnormal_mechanism:r.abnormal_mechanism,attrs:{release:r.release,environment:r.environment,ip_address:r.ipAddress,user_agent:r.userAgent}}}(n)};return t&&ut(n,t),n}function ut(t,e={}){if(e.user&&(!t.ipAddress&&e.user.ip_address&&(t.ipAddress=e.user.ip_address),t.did||e.did||(t.did=e.user.id||e.user.email||e.user.username)),t.timestamp=e.timestamp||te(),e.abnormal_mechanism&&(t.abnormal_mechanism=e.abnormal_mechanism),e.ignoreDuration&&(t.ignoreDuration=e.ignoreDuration),e.sid&&(t.sid=e.sid.length===32?e.sid:de()),e.init!==void 0&&(t.init=e.init),!t.did&&e.did&&(t.did=`${e.did}`),typeof e.started=="number"&&(t.started=e.started),t.ignoreDuration)t.duration=void 0;else if(typeof e.duration=="number")t.duration=e.duration;else{const n=t.timestamp-t.started;t.duration=n>=0?n:0}e.release&&(t.release=e.release),e.environment&&(t.environment=e.environment),!t.ipAddress&&e.ipAddress&&(t.ipAddress=e.ipAddress),!t.userAgent&&e.userAgent&&(t.userAgent=e.userAgent),typeof e.errors=="number"&&(t.errors=e.errors),e.status&&(t.status=e.status)}function qt(t,e,n=2){if(!e||typeof e!="object"||n<=0)return e;if(t&&Object.keys(e).length===0)return t;const r={...t};for(const s in e)Object.prototype.hasOwnProperty.call(e,s)&&(r[s]=qt(r[s],e[s],n-1));return r}const sr="_sentrySpan";function $t(t,e){e?ae(t,sr,e):delete t[sr]}function ln(t){return t[sr]}function Ae(){return de()}function Bt(){return de().substring(16)}class Te{constructor(){this._notifyingListeners=!1,this._scopeListeners=[],this._eventProcessors=[],this._breadcrumbs=[],this._attachments=[],this._user={},this._tags={},this._extra={},this._contexts={},this._sdkProcessingMetadata={},this._propagationContext={traceId:Ae(),sampleRand:Math.random()}}clone(){const e=new Te;return e._breadcrumbs=[...this._breadcrumbs],e._tags={...this._tags},e._extra={...this._extra},e._contexts={...this._contexts},this._contexts.flags&&(e._contexts.flags={values:[...this._contexts.flags.values]}),e._user=this._user,e._level=this._level,e._session=this._session,e._transactionName=this._transactionName,e._fingerprint=this._fingerprint,e._eventProcessors=[...this._eventProcessors],e._attachments=[...this._attachments],e._sdkProcessingMetadata={...this._sdkProcessingMetadata},e._propagationContext={...this._propagationContext},e._client=this._client,e._lastEventId=this._lastEventId,$t(e,ln(this)),e}setClient(e){this._client=e}setLastEventId(e){this._lastEventId=e}getClient(){return this._client}lastEventId(){return this._lastEventId}addScopeListener(e){this._scopeListeners.push(e)}addEventProcessor(e){return this._eventProcessors.push(e),this}setUser(e){return this._user=e||{email:void 0,id:void 0,ip_address:void 0,username:void 0},this._session&&ut(this._session,{user:e}),this._notifyScopeListeners(),this}getUser(){return this._user}setTags(e){return this._tags={...this._tags,...e},this._notifyScopeListeners(),this}setTag(e,n){return this._tags={...this._tags,[e]:n},this._notifyScopeListeners(),this}setExtras(e){return this._extra={...this._extra,...e},this._notifyScopeListeners(),this}setExtra(e,n){return this._extra={...this._extra,[e]:n},this._notifyScopeListeners(),this}setFingerprint(e){return this._fingerprint=e,this._notifyScopeListeners(),this}setLevel(e){return this._level=e,this._notifyScopeListeners(),this}setTransactionName(e){return this._transactionName=e,this._notifyScopeListeners(),this}setContext(e,n){return n===null?delete this._contexts[e]:this._contexts[e]=n,this._notifyScopeListeners(),this}setSession(e){return e?this._session=e:delete this._session,this._notifyScopeListeners(),this}getSession(){return this._session}update(e){if(!e)return this;const n=typeof e=="function"?e(this):e,r=n instanceof Te?n.getScopeData():kt(n)?e:void 0,{tags:s,extra:o,user:i,contexts:a,level:c,fingerprint:u=[],propagationContext:l}=r||{};return this._tags={...this._tags,...s},this._extra={...this._extra,...o},this._contexts={...this._contexts,...a},i&&Object.keys(i).length&&(this._user=i),c&&(this._level=c),u.length&&(this._fingerprint=u),l&&(this._propagationContext=l),this}clear(){return this._breadcrumbs=[],this._tags={},this._extra={},this._user={},this._contexts={},this._level=void 0,this._transactionName=void 0,this._fingerprint=void 0,this._session=void 0,$t(this,void 0),this._attachments=[],this.setPropagationContext({traceId:Ae(),sampleRand:Math.random()}),this._notifyScopeListeners(),this}addBreadcrumb(e,n){var o;const r=typeof n=="number"?n:100;if(r<=0)return this;const s={timestamp:Ft(),...e,message:e.message?dn(e.message,2048):e.message};return this._breadcrumbs.push(s),this._breadcrumbs.length>r&&(this._breadcrumbs=this._breadcrumbs.slice(-r),(o=this._client)==null||o.recordDroppedEvent("buffer_overflow","log_item")),this._notifyScopeListeners(),this}getLastBreadcrumb(){return this._breadcrumbs[this._breadcrumbs.length-1]}clearBreadcrumbs(){return this._breadcrumbs=[],this._notifyScopeListeners(),this}addAttachment(e){return this._attachments.push(e),this}clearAttachments(){return this._attachments=[],this}getScopeData(){return{breadcrumbs:this._breadcrumbs,attachments:this._attachments,contexts:this._contexts,tags:this._tags,extra:this._extra,user:this._user,level:this._level,fingerprint:this._fingerprint||[],eventProcessors:this._eventProcessors,propagationContext:this._propagationContext,sdkProcessingMetadata:this._sdkProcessingMetadata,transactionName:this._transactionName,span:ln(this)}}setSDKProcessingMetadata(e){return this._sdkProcessingMetadata=qt(this._sdkProcessingMetadata,e,2),this}setPropagationContext(e){return this._propagationContext=e,this}getPropagationContext(){return this._propagationContext}captureException(e,n){const r=(n==null?void 0:n.event_id)||de();if(!this._client)return w.warn("No client configured on scope - will not capture exception!"),r;const s=new Error("Sentry syntheticException");return this._client.captureException(e,{originalException:e,syntheticException:s,...n,event_id:r},this),r}captureMessage(e,n,r){const s=(r==null?void 0:r.event_id)||de();if(!this._client)return w.warn("No client configured on scope - will not capture message!"),s;const o=new Error(e);return this._client.captureMessage(e,n,{originalException:e,syntheticException:o,...r,event_id:s},this),s}captureEvent(e,n){const r=(n==null?void 0:n.event_id)||de();return this._client?(this._client.captureEvent(e,{...n,event_id:r},this),r):(w.warn("No client configured on scope - will not capture event!"),r)}_notifyScopeListeners(){this._notifyingListeners||(this._notifyingListeners=!0,this._scopeListeners.forEach(e=>{e(this)}),this._notifyingListeners=!1)}}class Bi{constructor(e,n){let r,s;r=e||new Te,s=n||new Te,this._stack=[{scope:r}],this._isolationScope=s}withScope(e){const n=this._pushScope();let r;try{r=e(n)}catch(s){throw this._popScope(),s}return Tn(r)?r.then(s=>(this._popScope(),s),s=>{throw this._popScope(),s}):(this._popScope(),r)}getClient(){return this.getStackTop().client}getScope(){return this.getStackTop().scope}getIsolationScope(){return this._isolationScope}getStackTop(){return this._stack[this._stack.length-1]}_pushScope(){const e=this.getScope().clone();return this._stack.push({client:this.getClient(),scope:e}),e}_popScope(){return!(this._stack.length<=1)&&!!this._stack.pop()}}function dt(){const t=En(nt());return t.stack=t.stack||new Bi(cn("defaultCurrentScope",()=>new Te),cn("defaultIsolationScope",()=>new Te))}function zi(t){return dt().withScope(t)}function Ui(t,e){const n=dt();return n.withScope(()=>(n.getStackTop().scope=t,e(t)))}function Vr(t){return dt().withScope(()=>t(dt().getIsolationScope()))}function _t(t){const e=En(t);return e.acs?e.acs:{withIsolationScope:Vr,withScope:zi,withSetScope:Ui,withSetIsolationScope:(n,r)=>Vr(r),getCurrentScope:()=>dt().getScope(),getIsolationScope:()=>dt().getIsolationScope()}}function q(){return _t(nt()).getCurrentScope()}function qe(){return _t(nt()).getIsolationScope()}function It(...t){const e=_t(nt());if(t.length===2){const[n,r]=t;return n?e.withSetScope(n,r):e.withScope(r)}return e.withScope(t[0])}function j(){return q().getClient()}function Hi(t){const e=t.getPropagationContext(),{traceId:n,parentSpanId:r,propagationSpanId:s}=e,o={trace_id:n,span_id:s||Bt()};return r&&(o.parent_span_id=r),o}const ye="sentry.source",$r="sentry.sample_rate",Co="sentry.previous_trace_sample_rate",Qe="sentry.op",W="sentry.origin",pn="sentry.idle_span_finish_reason",kn="sentry.measurement_unit",$n="sentry.measurement_value",Yr="sentry.custom_span_name",or="sentry.profile_id",Ct="sentry.exclusive_time",Wi="sentry.link.type",Ji=0,Po=1,U=2;function Ao(t,e){t.setAttribute("http.response.status_code",e);const n=function(r){if(r<400&&r>=100)return{code:Po};if(r>=400&&r<500)switch(r){case 401:return{code:U,message:"unauthenticated"};case 403:return{code:U,message:"permission_denied"};case 404:return{code:U,message:"not_found"};case 409:return{code:U,message:"already_exists"};case 413:return{code:U,message:"failed_precondition"};case 429:return{code:U,message:"resource_exhausted"};case 499:return{code:U,message:"cancelled"};default:return{code:U,message:"invalid_argument"}}if(r>=500&&r<600)switch(r){case 501:return{code:U,message:"unimplemented"};case 503:return{code:U,message:"unavailable"};case 504:return{code:U,message:"deadline_exceeded"};default:return{code:U,message:"internal_error"}}return{code:U,message:"unknown_error"}}(e);n.message!=="unknown_error"&&t.setStatus(n)}const Oo="_sentryScope",Ro="_sentryIsolationScope";function fn(t){return{scope:t[Oo],isolationScope:t[Ro]}}function Pt(t){if(typeof t=="boolean")return Number(t);const e=typeof t=="string"?parseFloat(t):t;return typeof e!="number"||isNaN(e)||e<0||e>1?void 0:e}const Ir="sentry-",Gi=/^sentry-/,Vi=8192;function Do(t){const e=function(r){if(!(!r||!Se(r)&&!Array.isArray(r)))return Array.isArray(r)?r.reduce((s,o)=>{const i=Kr(o);return Object.entries(i).forEach(([a,c])=>{s[a]=c}),s},{}):Kr(r)}(t);if(!e)return;const n=Object.entries(e).reduce((r,[s,o])=>(s.match(Gi)&&(r[s.slice(Ir.length)]=o),r),{});return Object.keys(n).length>0?n:void 0}function Yi(t){if(t)return function(e){if(Object.keys(e).length!==0)return Object.entries(e).reduce((n,[r,s],o)=>{const i=`${encodeURIComponent(r)}=${encodeURIComponent(s)}`,a=o===0?i:`${n},${i}`;return a.length>Vi?($&&w.warn(`Not adding key: ${r} with val: ${s} to baggage header due to exceeding baggage size limits.`),n):a},"")}(Object.entries(t).reduce((e,[n,r])=>(r&&(e[`${Ir}${n}`]=r),e),{}))}function Kr(t){return t.split(",").map(e=>e.split("=").map(n=>{try{return decodeURIComponent(n.trim())}catch{return}})).reduce((e,[n,r])=>(n&&r&&(e[n]=r),e),{})}const Lo=new RegExp("^[ \\t]*([0-9a-f]{32})?-?([0-9a-f]{16})?-?([01])?[ \\t]*$");function Ki(t,e){const n=function(c){if(!c)return;const u=c.match(Lo);if(!u)return;let l;return u[3]==="1"?l=!0:u[3]==="0"&&(l=!1),{traceId:u[1],parentSampled:l,parentSpanId:u[2]}}(t),r=Do(e);if(!(n!=null&&n.traceId))return{traceId:Ae(),sampleRand:Math.random()};const s=function(c,u){const l=Pt(u==null?void 0:u.sample_rand);if(l!==void 0)return l;const h=Pt(u==null?void 0:u.sample_rate);return h&&(c==null?void 0:c.parentSampled)!==void 0?c.parentSampled?Math.random()*h:h+Math.random()*(1-h):Math.random()}(n,r);r&&(r.sample_rand=s.toString());const{traceId:o,parentSpanId:i,parentSampled:a}=n;return{traceId:o,parentSpanId:i,sampled:a,dsc:r||{},sampleRand:s}}function Xr(t=Ae(),e=Bt(),n){let r="";return n!==void 0&&(r=n?"-1":"-0"),`${t}-${e}${r}`}const Cr=1;let Zr=!1;function Xi(t){const{spanId:e,traceId:n}=t.spanContext(),{data:r,op:s,parent_span_id:o,status:i,origin:a,links:c}=F(t);return{parent_span_id:o,span_id:e,trace_id:n,data:r,op:s,status:i,origin:a,links:c}}function Zi(t){const{spanId:e,traceId:n,isRemote:r}=t.spanContext(),s=r?e:F(t).parent_span_id,o=fn(t).scope;return{parent_span_id:s,span_id:r?(o==null?void 0:o.getPropagationContext().propagationSpanId)||Bt():e,trace_id:n}}function No(t){return t&&t.length>0?t.map(({context:{spanId:e,traceId:n,traceFlags:r,...s},attributes:o})=>({span_id:e,trace_id:n,sampled:r===Cr,attributes:o,...s})):void 0}function Ge(t){return typeof t=="number"?Qr(t):Array.isArray(t)?t[0]+t[1]/1e9:t instanceof Date?Qr(t.getTime()):te()}function Qr(t){return t>9999999999?t/1e3:t}function F(t){var r;if(function(s){return typeof s.getSpanJSON=="function"}(t))return t.getSpanJSON();const{spanId:e,traceId:n}=t.spanContext();if(function(s){const o=s;return!!(o.attributes&&o.startTime&&o.name&&o.endTime&&o.status)}(t)){const{attributes:s,startTime:o,name:i,endTime:a,status:c,links:u}=t;return{span_id:e,trace_id:n,data:s,description:i,parent_span_id:"parentSpanId"in t?t.parentSpanId:"parentSpanContext"in t?(r=t.parentSpanContext)==null?void 0:r.spanId:void 0,start_timestamp:Ge(o),timestamp:Ge(a)||void 0,status:Mo(c),op:s[Qe],origin:s[W],links:No(u)}}return{span_id:e,trace_id:n,start_timestamp:0,data:{}}}function Ve(t){const{traceFlags:e}=t.spanContext();return e===Cr}function Mo(t){if(t&&t.code!==Ji)return t.code===Po?"ok":t.message||"unknown_error"}const Ye="_sentryChildSpans",ir="_sentryRootSpan";function es(t,e){const n=t[ir]||t;ae(e,ir,n),t[Ye]?t[Ye].add(e):ae(t,Ye,new Set([e]))}function tn(t){const e=new Set;return function n(r){if(!e.has(r)&&Ve(r)){e.add(r);const s=r[Ye]?Array.from(r[Ye]):[];for(const o of s)n(o)}}(t),Array.from(e)}function ee(t){return t[ir]||t}function se(){const t=_t(nt());return t.getActiveSpan?t.getActiveSpan():ln(q())}function ar(){Zr||(rt(()=>{console.warn("[Sentry] Returning null from `beforeSendSpan` is disallowed. To drop certain spans, configure the respective integrations directly.")}),Zr=!0)}const ts=50,Ke="?",ns=/\(error: (.*)\)/,rs=/captureMessage|captureException/;function jo(...t){const e=t.sort((n,r)=>n[0]-r[0]).map(n=>n[1]);return(n,r=0,s=0)=>{const o=[],i=n.split(`
`);for(let a=r;a<i.length;a++){const c=i[a];if(c.length>1024)continue;const u=ns.test(c)?c.replace(ns,"$1"):c;if(!u.match(/\S*Error: /)){for(const l of e){const h=l(u);if(h){o.push(h);break}}if(o.length>=ts+s)break}}return function(a){if(!a.length)return[];const c=Array.from(a);return/sentryWrapped/.test(Wt(c).function||"")&&c.pop(),c.reverse(),rs.test(Wt(c).function||"")&&(c.pop(),rs.test(Wt(c).function||"")&&c.pop()),c.slice(0,ts).map(u=>({...u,filename:u.filename||Wt(c).filename,function:u.function||Ke}))}(o.slice(s))}}function Wt(t){return t[t.length-1]||{}}const ss="<anonymous>";function ke(t){try{return t&&typeof t=="function"&&t.name||ss}catch{return ss}}function os(t){const e=t.exception;if(e){const n=[];try{return e.values.forEach(r=>{r.stacktrace.frames&&n.push(...r.stacktrace.frames)}),n}catch{return}}}const nn={},is={};function Oe(t,e){nn[t]=nn[t]||[],nn[t].push(e)}function Re(t,e){if(!is[t]){is[t]=!0;try{e()}catch(n){$&&w.error(`Error while instrumenting ${t}`,n)}}}function le(t,e){const n=t&&nn[t];if(n)for(const r of n)try{r(e)}catch(s){$&&w.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${ke(r)}
Error:`,s)}}let Mn=null;function Fo(t){const e="error";Oe(e,t),Re(e,Qi)}function Qi(){Mn=N.onerror,N.onerror=function(t,e,n,r,s){return le("error",{column:r,error:s,line:n,msg:t,url:e}),!!Mn&&Mn.apply(this,arguments)},N.onerror.__SENTRY_INSTRUMENTED__=!0}let jn=null;function qo(t){const e="unhandledrejection";Oe(e,t),Re(e,ea)}function ea(){jn=N.onunhandledrejection,N.onunhandledrejection=function(t){return le("unhandledrejection",t),!jn||jn.apply(this,arguments)},N.onunhandledrejection.__SENTRY_INSTRUMENTED__=!0}let as=!1;function cr(){const t=se(),e=t&&ee(t);if(e){const n="internal_error";$&&w.log(`[Tracing] Root span: ${n} -> Global error occurred`),e.setStatus({code:U,message:n})}}function De(t){var n;if(typeof __SENTRY_TRACING__=="boolean"&&!__SENTRY_TRACING__)return!1;const e=t||((n=j())==null?void 0:n.getOptions());return!(!e||e.tracesSampleRate==null&&!e.tracesSampler)}cr.tag="sentry_tracingErrorCallback";const Pr="production",ta=/^o(\d+)\./,na=/^(?:(\w+):)\/\/(?:(\w+)(?::(\w+)?)?@)([\w.-]+)(?::(\d+))?\/(.+)/;function At(t,e=!1){const{host:n,path:r,pass:s,port:o,projectId:i,protocol:a,publicKey:c}=t;return`${a}://${c}${e&&s?`:${s}`:""}@${n}${o?`:${o}`:""}/${r&&`${r}/`}${i}`}function cs(t){return{protocol:t.protocol,publicKey:t.publicKey||"",pass:t.pass||"",host:t.host,port:t.port||"",path:t.path||"",projectId:t.projectId}}function ra(t){const e=typeof t=="string"?function(n){const r=na.exec(n);if(!r)return void rt(()=>{console.error(`Invalid Sentry Dsn: ${n}`)});const[s,o,i="",a="",c="",u=""]=r.slice(1);let l="",h=u;const f=h.split("/");if(f.length>1&&(l=f.slice(0,-1).join("/"),h=f.pop()),h){const d=h.match(/^\d+/);d&&(h=d[0])}return cs({host:a,pass:i,path:l,projectId:h,port:c,protocol:s,publicKey:o})}(t):cs(t);if(e&&function(n){if(!$)return!0;const{port:r,projectId:s,protocol:o}=n;return!(["protocol","publicKey","host","projectId"].find(i=>!n[i]&&(w.error(`Invalid Sentry Dsn: ${i} missing`),!0))||(s.match(/^\d+$/)?function(i){return i==="http"||i==="https"}(o)?r&&isNaN(parseInt(r,10))&&(w.error(`Invalid Sentry Dsn: Invalid port ${r}`),1):(w.error(`Invalid Sentry Dsn: Invalid protocol ${o}`),1):(w.error(`Invalid Sentry Dsn: Invalid projectId ${s}`),1)))}(e))return e}const Bo="_frozenDsc";function rn(t,e){ae(t,Bo,e)}function zo(t,e){const n=e.getOptions(),{publicKey:r,host:s}=e.getDsn()||{};let o;n.orgId?o=String(n.orgId):s&&(o=function(a){const c=a.match(ta);return c==null?void 0:c[1]}(s));const i={environment:n.environment||Pr,release:n.release,public_key:r,trace_id:t,org_id:o};return e.emit("createDsc",i),i}function Uo(t,e){const n=e.getPropagationContext();return n.dsc||zo(n.traceId,t)}function Le(t){var p;const e=j();if(!e)return{};const n=ee(t),r=F(n),s=r.data,o=n.spanContext().traceState,i=(o==null?void 0:o.get("sentry.sample_rate"))??s[$r]??s[Co];function a(m){return typeof i!="number"&&typeof i!="string"||(m.sample_rate=`${i}`),m}const c=n[Bo];if(c)return a(c);const u=o==null?void 0:o.get("sentry.dsc"),l=u&&Do(u);if(l)return a(l);const h=zo(t.spanContext().traceId,e),f=s[ye],d=r.description;return f!=="url"&&d&&(h.transaction=d),De()&&(h.sampled=String(Ve(n)),h.sample_rand=(o==null?void 0:o.get("sentry.sample_rand"))??((p=fn(n).scope)==null?void 0:p.getPropagationContext().sampleRand.toString())),a(h),e.emit("createDsc",h,n),h}class Xe{constructor(e={}){this._traceId=e.traceId||Ae(),this._spanId=e.spanId||Bt()}spanContext(){return{spanId:this._spanId,traceId:this._traceId,traceFlags:0}}end(e){}setAttribute(e,n){return this}setAttributes(e){return this}setStatus(e){return this}updateName(e){return this}isRecording(){return!1}addEvent(e,n,r){return this}addLink(e){return this}addLinks(e){return this}recordException(e,n){}}function ge(t,e=100,n=1/0){try{return ur("",t,e,n)}catch(r){return{ERROR:`**non-serializable** (${r})`}}}function Ho(t,e=3,n=102400){const r=ge(t,e);return s=r,function(o){return~-encodeURI(o).split(/%..|./).length}(JSON.stringify(s))>n?Ho(t,e-1,n):r;var s}function ur(t,e,n=1/0,r=1/0,s=function(){const o=new WeakSet;function i(c){return!!o.has(c)||(o.add(c),!1)}function a(c){o.delete(c)}return[i,a]}()){const[o,i]=s;if(e==null||["boolean","string"].includes(typeof e)||typeof e=="number"&&Number.isFinite(e))return e;const a=function(d,p){try{if(d==="domain"&&p&&typeof p=="object"&&p._events)return"[Domain]";if(d==="domainEmitter")return"[DomainEmitter]";if(typeof global<"u"&&p===global)return"[Global]";if(typeof window<"u"&&p===window)return"[Window]";if(typeof document<"u"&&p===document)return"[Document]";if(wo(p))return"[VueViewModel]";if(kt(m=p)&&"nativeEvent"in m&&"preventDefault"in m&&"stopPropagation"in m)return"[SyntheticEvent]";if(typeof p=="number"&&!Number.isFinite(p))return`[${p}]`;if(typeof p=="function")return`[Function: ${ke(p)}]`;if(typeof p=="symbol")return`[${String(p)}]`;if(typeof p=="bigint")return`[BigInt: ${String(p)}]`;const g=function(v){const y=Object.getPrototypeOf(v);return y!=null&&y.constructor?y.constructor.name:"null prototype"}(p);return/^HTML(\w*)Element$/.test(g)?`[HTMLElement: ${g}]`:`[object ${g}]`}catch(g){return`**non-serializable** (${g})`}var m}(t,e);if(!a.startsWith("[object "))return a;if(e.__sentry_skip_normalization__)return e;const c=typeof e.__sentry_override_normalization_depth__=="number"?e.__sentry_override_normalization_depth__:n;if(c===0)return a.replace("object ","");if(o(e))return"[Circular ~]";const u=e;if(u&&typeof u.toJSON=="function")try{return ur("",u.toJSON(),c-1,r,s)}catch{}const l=Array.isArray(e)?[]:{};let h=0;const f=ko(e);for(const d in f){if(!Object.prototype.hasOwnProperty.call(f,d))continue;if(h>=r){l[d]="[MaxProperties ~]";break}const p=f[d];l[d]=ur(d,p,c-1,r,s),h++}return i(e),l}function lt(t,e=[]){return[t,e]}function sa(t,e){const[n,r]=t;return[n,[...r,e]]}function us(t,e){const n=t[1];for(const r of n)if(e(r,r[0].type))return!0;return!1}function dr(t){const e=En(N);return e.encodePolyfill?e.encodePolyfill(t):new TextEncoder().encode(t)}function oa(t){const[e,n]=t;let r=JSON.stringify(e);function s(o){typeof r=="string"?r=typeof o=="string"?r+o:[dr(r),o]:r.push(typeof o=="string"?dr(o):o)}for(const o of n){const[i,a]=o;if(s(`
${JSON.stringify(i)}
`),typeof a=="string"||a instanceof Uint8Array)s(a);else{let c;try{c=JSON.stringify(a)}catch{c=JSON.stringify(ge(a))}s(c)}}return typeof r=="string"?r:function(o){const i=o.reduce((u,l)=>u+l.length,0),a=new Uint8Array(i);let c=0;for(const u of o)a.set(u,c),c+=u.length;return a}(r)}function ia(t){return[{type:"span"},t]}function aa(t){const e=typeof t.data=="string"?dr(t.data):t.data;return[{type:"attachment",length:e.length,filename:t.filename,content_type:t.contentType,attachment_type:t.attachmentType},e]}const ca={session:"session",sessions:"session",attachment:"attachment",transaction:"transaction",event:"error",client_report:"internal",user_report:"default",profile:"profile",profile_chunk:"profile",replay_event:"replay",replay_recording:"replay",check_in:"monitor",feedback:"feedback",span:"span",raw_security:"security",log:"log_item"};function ds(t){return ca[t]}function Wo(t){if(!(t!=null&&t.sdk))return;const{name:e,version:n}=t.sdk;return{name:e,version:n}}function ua(t,e,n,r){const s=Wo(n),o=t.type&&t.type!=="replay_event"?t.type:"event";(function(a,c){c&&(a.sdk=a.sdk||{},a.sdk.name=a.sdk.name||c.name,a.sdk.version=a.sdk.version||c.version,a.sdk.integrations=[...a.sdk.integrations||[],...c.integrations||[]],a.sdk.packages=[...a.sdk.packages||[],...c.packages||[]])})(t,n==null?void 0:n.sdk);const i=function(a,c,u,l){var f;const h=(f=a.sdkProcessingMetadata)==null?void 0:f.dynamicSamplingContext;return{event_id:a.event_id,sent_at:new Date().toISOString(),...c&&{sdk:c},...!!u&&l&&{dsn:At(l)},...h&&{trace:h}}}(t,s,r,e);return delete t.sdkProcessingMetadata,lt(i,[[{type:o},t]])}function ls(t){if(!t||t.length===0)return;const e={};return t.forEach(n=>{const r=n.attributes||{},s=r[kn],o=r[$n];typeof s=="string"&&typeof o=="number"&&(e[n.name]={value:o,unit:s})}),e}class In{constructor(e={}){this._traceId=e.traceId||Ae(),this._spanId=e.spanId||Bt(),this._startTime=e.startTimestamp||te(),this._links=e.links,this._attributes={},this.setAttributes({[W]:"manual",[Qe]:e.op,...e.attributes}),this._name=e.name,e.parentSpanId&&(this._parentSpanId=e.parentSpanId),"sampled"in e&&(this._sampled=e.sampled),e.endTimestamp&&(this._endTime=e.endTimestamp),this._events=[],this._isStandaloneSpan=e.isStandalone,this._endTime&&this._onSpanEnded()}addLink(e){return this._links?this._links.push(e):this._links=[e],this}addLinks(e){return this._links?this._links.push(...e):this._links=e,this}recordException(e,n){}spanContext(){const{_spanId:e,_traceId:n,_sampled:r}=this;return{spanId:e,traceId:n,traceFlags:r?Cr:0}}setAttribute(e,n){return n===void 0?delete this._attributes[e]:this._attributes[e]=n,this}setAttributes(e){return Object.keys(e).forEach(n=>this.setAttribute(n,e[n])),this}updateStartTime(e){this._startTime=Ge(e)}setStatus(e){return this._status=e,this}updateName(e){return this._name=e,this.setAttribute(ye,"custom"),this}end(e){this._endTime||(this._endTime=Ge(e),function(n){if(!$)return;const{description:r="< unknown name >",op:s="< unknown op >"}=F(n),{spanId:o}=n.spanContext(),i=`[Tracing] Finishing "${s}" ${ee(n)===n?"root ":""}span "${r}" with ID ${o}`;w.log(i)}(this),this._onSpanEnded())}getSpanJSON(){return{data:this._attributes,description:this._name,op:this._attributes[Qe],parent_span_id:this._parentSpanId,span_id:this._spanId,start_timestamp:this._startTime,status:Mo(this._status),timestamp:this._endTime,trace_id:this._traceId,origin:this._attributes[W],profile_id:this._attributes[or],exclusive_time:this._attributes[Ct],measurements:ls(this._events),is_segment:this._isStandaloneSpan&&ee(this)===this||void 0,segment_id:this._isStandaloneSpan?ee(this).spanContext().spanId:void 0,links:No(this._links)}}isRecording(){return!this._endTime&&!!this._sampled}addEvent(e,n,r){$&&w.log("[Tracing] Adding an event to span:",e);const s=ps(n)?n:r||te(),o=ps(n)?{}:n||{},i={name:e,time:Ge(s),attributes:o};return this._events.push(i),this}isStandaloneSpan(){return!!this._isStandaloneSpan}_onSpanEnded(){const e=j();if(e&&e.emit("spanEnd",this),!(this._isStandaloneSpan||this===ee(this)))return;if(this._isStandaloneSpan)return void(this._sampled?function(r){const s=j();if(!s)return;const o=r[1];if(!o||o.length===0)return void s.recordDroppedEvent("before_send","span");s.sendEnvelope(r)}(function(r,s){const o=Le(r[0]),i=s==null?void 0:s.getDsn(),a=s==null?void 0:s.getOptions().tunnel,c={sent_at:new Date().toISOString(),...function(f){return!!f.trace_id&&!!f.public_key}(o)&&{trace:o},...!!a&&i&&{dsn:At(i)}},u=s==null?void 0:s.getOptions().beforeSendSpan,l=u?f=>{const d=F(f);return u(d)||(ar(),d)}:F,h=[];for(const f of r){const d=l(f);d&&h.push(ia(d))}return lt(c,h)}([this],e)):($&&w.log("[Tracing] Discarding standalone span because its trace was not chosen to be sampled."),e&&e.recordDroppedEvent("sample_rate","span")));const n=this._convertSpanToTransaction();n&&(fn(this).scope||q()).captureEvent(n)}_convertSpanToTransaction(){var c;if(!fs(F(this)))return;this._name||($&&w.warn("Transaction has no name, falling back to `<unlabeled transaction>`."),this._name="<unlabeled transaction>");const{scope:e,isolationScope:n}=fn(this),r=(c=e==null?void 0:e.getScopeData().sdkProcessingMetadata)==null?void 0:c.normalizedRequest;if(this._sampled!==!0)return;const s=tn(this).filter(u=>u!==this&&!function(l){return l instanceof In&&l.isStandaloneSpan()}(u)).map(u=>F(u)).filter(fs),o=this._attributes[ye];delete this._attributes[Yr],s.forEach(u=>{delete u.data[Yr]});const i={contexts:{trace:Xi(this)},spans:s.length>1e3?s.sort((u,l)=>u.start_timestamp-l.start_timestamp).slice(0,1e3):s,start_timestamp:this._startTime,timestamp:this._endTime,transaction:this._name,type:"transaction",sdkProcessingMetadata:{capturedSpanScope:e,capturedSpanIsolationScope:n,dynamicSamplingContext:Le(this)},request:r,...o&&{transaction_info:{source:o}}},a=ls(this._events);return a&&Object.keys(a).length&&($&&w.log("[Measurements] Adding measurements to transaction event",JSON.stringify(a,void 0,2)),i.measurements=a),i}}function ps(t){return t&&typeof t=="number"||t instanceof Date||Array.isArray(t)}function fs(t){return!!(t.start_timestamp&&t.timestamp&&t.span_id&&t.trace_id)}const Jo="__SENTRY_SUPPRESS_TRACING__";function zt(t){const e=Vo();if(e.startInactiveSpan)return e.startInactiveSpan(t);const n=function(o){const i=o.experimental||{},a={isStandalone:i.standalone,...o};if(o.startTime){const c={...a};return c.startTimestamp=Ge(o.startTime),delete c.startTime,c}return a}(t),{forceTransaction:r,parentSpan:s}=t;return(t.scope?o=>It(t.scope,o):s!==void 0?o=>Go(s,o):o=>o())(()=>{const o=q(),i=function(a){const c=ln(a);if(!c)return;const u=j();return(u?u.getOptions():{}).parentSpanIsAlwaysRootSpan?ee(c):c}(o);return t.onlyIfParent&&!i?new Xe:function({parentSpan:a,spanArguments:c,forceTransaction:u,scope:l}){if(!De()){const d=new Xe;return(u||!a)&&rn(d,{sampled:"false",sample_rate:"0",transaction:c.name,...Le(d)}),d}const h=qe();let f;if(a&&!u)f=function(d,p,m){const{spanId:g,traceId:v}=d.spanContext(),y=!p.getScopeData().sdkProcessingMetadata[Jo]&&Ve(d),E=y?new In({...m,parentSpanId:g,traceId:v,sampled:y}):new Xe({traceId:v});es(d,E);const _=j();return _&&(_.emit("spanStart",E),m.endTimestamp&&_.emit("spanEnd",E)),E}(a,l,c),es(a,f);else if(a){const d=Le(a),{traceId:p,spanId:m}=a.spanContext(),g=Ve(a);f=hs({traceId:p,parentSpanId:m,...c},l,g),rn(f,d)}else{const{traceId:d,dsc:p,parentSpanId:m,sampled:g}={...h.getPropagationContext(),...l.getPropagationContext()};f=hs({traceId:d,parentSpanId:m,...c},l,g),p&&rn(f,p)}return function(d){if(!$)return;const{description:p="< unknown name >",op:m="< unknown op >",parent_span_id:g}=F(d),{spanId:v}=d.spanContext(),y=Ve(d),E=ee(d),_=E===d,b=`[Tracing] Starting ${y?"sampled":"unsampled"} ${_?"root ":""}span`,T=[`op: ${m}`,`name: ${p}`,`ID: ${v}`];if(g&&T.push(`parent ID: ${g}`),!_){const{op:x,description:D}=F(E);T.push(`root ID: ${E.spanContext().spanId}`),x&&T.push(`root op: ${x}`),D&&T.push(`root description: ${D}`)}w.log(`${b}
  ${T.join(`
  `)}`)}(f),function(d,p,m){d&&(ae(d,Ro,m),ae(d,Oo,p))}(f,l,h),f}({parentSpan:i,spanArguments:n,forceTransaction:r,scope:o})})}function Go(t,e){const n=Vo();return n.withActiveSpan?n.withActiveSpan(t,e):It(r=>($t(r,t||void 0),e(r)))}function Vo(){return _t(nt())}function hs(t,e,n){var p;const r=j(),s=(r==null?void 0:r.getOptions())||{},{name:o=""}=t,i={spanAttributes:{...t.attributes},spanName:o,parentSampled:n};r==null||r.emit("beforeSampling",i,{decision:!1});const a=i.parentSampled??n,c=i.spanAttributes,u=e.getPropagationContext(),[l,h,f]=e.getScopeData().sdkProcessingMetadata[Jo]?[!1]:function(m,g,v){if(!De(m))return[!1];let y,E;typeof m.tracesSampler=="function"?(E=m.tracesSampler({...g,inheritOrSampleWith:T=>typeof g.parentSampleRate=="number"?g.parentSampleRate:typeof g.parentSampled=="boolean"?Number(g.parentSampled):T}),y=!0):g.parentSampled!==void 0?E=g.parentSampled:m.tracesSampleRate!==void 0&&(E=m.tracesSampleRate,y=!0);const _=Pt(E);if(_===void 0)return $&&w.warn(`[Tracing] Discarding root span because of invalid sample rate. Sample rate must be a boolean or a number between 0 and 1. Got ${JSON.stringify(E)} of type ${JSON.stringify(typeof E)}.`),[!1];if(!_)return $&&w.log("[Tracing] Discarding transaction because "+(typeof m.tracesSampler=="function"?"tracesSampler returned 0 or false":"a negative sampling decision was inherited or tracesSampleRate is set to 0")),[!1,_,y];const b=v<_;return b||$&&w.log(`[Tracing] Discarding transaction because it's not included in the random sample (sampling rate = ${Number(E)})`),[b,_,y]}(s,{name:o,parentSampled:a,attributes:c,parentSampleRate:Pt((p=u.dsc)==null?void 0:p.sample_rate)},u.sampleRand),d=new In({...t,attributes:{[ye]:"custom",[$r]:h!==void 0&&f?h:void 0,...c},sampled:l});return!l&&r&&($&&w.log("[Tracing] Discarding root span because its trace was not chosen to be sampled."),r.recordDroppedEvent("sample_rate","transaction")),r&&r.emit("spanStart",d),d}const sn={idleTimeout:1e3,finalTimeout:3e4,childSpanTimeout:15e3},da="heartbeatFailed",la="idleTimeout",pa="finalTimeout",fa="externalFinish";function ms(t,e={}){const n=new Map;let r,s=!1,o=fa,i=!e.disableAutoFinish;const a=[],{idleTimeout:c=sn.idleTimeout,finalTimeout:u=sn.finalTimeout,childSpanTimeout:l=sn.childSpanTimeout,beforeSpanEnd:h}=e,f=j();if(!f||!De()){const _=new Xe;return rn(_,{sample_rate:"0",sampled:"false",...Le(_)}),_}const d=q(),p=se(),m=function(_){const b=zt(_);return $t(q(),b),$&&w.log("[Tracing] Started span is an idle span"),b}(t);function g(){r&&(clearTimeout(r),r=void 0)}function v(_){g(),r=setTimeout(()=>{!s&&n.size===0&&i&&(o=la,m.end(_))},c)}function y(_){r=setTimeout(()=>{!s&&i&&(o=da,m.end(_))},l)}function E(_){s=!0,n.clear(),a.forEach(S=>S()),$t(d,p);const b=F(m),{start_timestamp:T}=b;if(!T)return;b.data[pn]||m.setAttribute(pn,o),w.log(`[Tracing] Idle span "${b.op}" finished`);const x=tn(m).filter(S=>S!==m);let D=0;x.forEach(S=>{S.isRecording()&&(S.setStatus({code:U,message:"cancelled"}),S.end(_),$&&w.log("[Tracing] Cancelling span since span ended early",JSON.stringify(S,void 0,2)));const O=F(S),{timestamp:R=0,start_timestamp:k=0}=O,C=k<=_,P=R-k<=(u+c)/1e3;if($){const I=JSON.stringify(S,void 0,2);C?P||w.log("[Tracing] Discarding span since it finished after idle span final timeout",I):w.log("[Tracing] Discarding span since it happened after idle span was finished",I)}P&&C||(function(I,M){I[Ye]&&I[Ye].delete(M)}(m,S),D++)}),D>0&&m.setAttribute("sentry.idle_span_discarded_spans",D)}return m.end=new Proxy(m.end,{apply(_,b,T){if(h&&h(m),b instanceof Xe)return;const[x,...D]=T,S=Ge(x||te()),O=tn(m).filter(I=>I!==m);if(!O.length)return E(S),Reflect.apply(_,b,[S,...D]);const R=O.map(I=>F(I).timestamp).filter(I=>!!I),k=R.length?Math.max(...R):void 0,C=F(m).start_timestamp,P=Math.min(C?C+u/1e3:1/0,Math.max(C||-1/0,Math.min(S,k||1/0)));return E(P),Reflect.apply(_,b,[P,...D])}}),a.push(f.on("spanStart",_=>{if(!(s||_===m||F(_).timestamp)){var b;tn(m).includes(_)&&(b=_.spanContext().spanId,g(),n.set(b,!0),y(te()+l/1e3))}})),a.push(f.on("spanEnd",_=>{var b;s||(b=_.spanContext().spanId,n.has(b)&&n.delete(b),n.size===0&&v(te()+c/1e3))})),a.push(f.on("idleSpanEnableAutoFinish",_=>{_===m&&(i=!0,v(),n.size&&y())})),e.disableAutoFinish||v(),setTimeout(()=>{s||(m.setStatus({code:U,message:"deadline_exceeded"}),o=pa,m.end())},u),m}var _e;function et(t){return new Ne(e=>{e(t)})}function hn(t){return new Ne((e,n)=>{n(t)})}(function(t){t[t.PENDING=0]="PENDING",t[t.RESOLVED=1]="RESOLVED",t[t.REJECTED=2]="REJECTED"})(_e||(_e={}));class Ne{constructor(e){this._state=_e.PENDING,this._handlers=[],this._runExecutor(e)}then(e,n){return new Ne((r,s)=>{this._handlers.push([!1,o=>{if(e)try{r(e(o))}catch(i){s(i)}else r(o)},o=>{if(n)try{r(n(o))}catch(i){s(i)}else s(o)}]),this._executeHandlers()})}catch(e){return this.then(n=>n,e)}finally(e){return new Ne((n,r)=>{let s,o;return this.then(i=>{o=!1,s=i,e&&e()},i=>{o=!0,s=i,e&&e()}).then(()=>{o?r(s):n(s)})})}_executeHandlers(){if(this._state===_e.PENDING)return;const e=this._handlers.slice();this._handlers=[],e.forEach(n=>{n[0]||(this._state===_e.RESOLVED&&n[1](this._value),this._state===_e.REJECTED&&n[2](this._value),n[0]=!0)})}_runExecutor(e){const n=(o,i)=>{this._state===_e.PENDING&&(Tn(i)?i.then(r,s):(this._state=o,this._value=i,this._executeHandlers()))},r=o=>{n(_e.RESOLVED,o)},s=o=>{n(_e.REJECTED,o)};try{e(r,s)}catch(o){s(o)}}}function lr(t,e,n,r=0){return new Ne((s,o)=>{const i=t[r];if(e===null||typeof i!="function")s(e);else{const a=i({...e},n);$&&i.id&&a===null&&w.log(`Event processor "${i.id}" dropped event`),Tn(a)?a.then(c=>lr(t,c,n,r+1).then(s)).then(null,o):lr(t,a,n,r+1).then(s).then(null,o)}})}let Jt,gs,Fn;function ha(t,e){const{fingerprint:n,span:r,breadcrumbs:s,sdkProcessingMetadata:o}=e;(function(i,a){const{extra:c,tags:u,user:l,contexts:h,level:f,transactionName:d}=a;Object.keys(c).length&&(i.extra={...c,...i.extra}),Object.keys(u).length&&(i.tags={...u,...i.tags}),Object.keys(l).length&&(i.user={...l,...i.user}),Object.keys(h).length&&(i.contexts={...h,...i.contexts}),f&&(i.level=f),d&&i.type!=="transaction"&&(i.transaction=d)})(t,e),r&&function(i,a){i.contexts={trace:Zi(a),...i.contexts},i.sdkProcessingMetadata={dynamicSamplingContext:Le(a),...i.sdkProcessingMetadata};const c=ee(a),u=F(c).description;u&&!i.transaction&&i.type==="transaction"&&(i.transaction=u)}(t,r),function(i,a){i.fingerprint=i.fingerprint?Array.isArray(i.fingerprint)?i.fingerprint:[i.fingerprint]:[],a&&(i.fingerprint=i.fingerprint.concat(a)),i.fingerprint.length||delete i.fingerprint}(t,n),function(i,a){const c=[...i.breadcrumbs||[],...a];i.breadcrumbs=c.length?c:void 0}(t,s),function(i,a){i.sdkProcessingMetadata={...i.sdkProcessingMetadata,...a}}(t,o)}function _s(t,e){const{extra:n,tags:r,user:s,contexts:o,level:i,sdkProcessingMetadata:a,breadcrumbs:c,fingerprint:u,eventProcessors:l,attachments:h,propagationContext:f,transactionName:d,span:p}=e;Gt(t,"extra",n),Gt(t,"tags",r),Gt(t,"user",s),Gt(t,"contexts",o),t.sdkProcessingMetadata=qt(t.sdkProcessingMetadata,a,2),i&&(t.level=i),d&&(t.transactionName=d),p&&(t.span=p),c.length&&(t.breadcrumbs=[...t.breadcrumbs,...c]),u.length&&(t.fingerprint=[...t.fingerprint,...u]),l.length&&(t.eventProcessors=[...t.eventProcessors,...l]),h.length&&(t.attachments=[...t.attachments,...h]),t.propagationContext={...t.propagationContext,...f}}function Gt(t,e,n){t[e]=qt(t[e],n,1)}function ma(t,e,n,r,s,o){const{normalizeDepth:i=3,normalizeMaxBreadth:a=1e3}=t,c={...e,event_id:e.event_id||n.event_id||de(),timestamp:e.timestamp||Ft()},u=n.integrations||t.integrations.map(p=>p.name);(function(p,m){const{environment:g,release:v,dist:y,maxValueLength:E=250}=m;p.environment=p.environment||g||Pr,!p.release&&v&&(p.release=v),!p.dist&&y&&(p.dist=y);const _=p.request;_!=null&&_.url&&(_.url=dn(_.url,E))})(c,t),function(p,m){m.length>0&&(p.sdk=p.sdk||{},p.sdk.integrations=[...p.sdk.integrations||[],...m])}(c,u),s&&s.emit("applyFrameMetadata",e),e.type===void 0&&function(p,m){var v,y;const g=function(E){const _=N._sentryDebugIds;if(!_)return{};const b=Object.keys(_);return Fn&&b.length===gs||(gs=b.length,Fn=b.reduce((T,x)=>{Jt||(Jt={});const D=Jt[x];if(D)T[D[0]]=D[1];else{const S=E(x);for(let O=S.length-1;O>=0;O--){const R=S[O],k=R==null?void 0:R.filename,C=_[x];if(k&&C){T[k]=C,Jt[x]=[k,C];break}}}return T},{})),Fn}(m);(y=(v=p.exception)==null?void 0:v.values)==null||y.forEach(E=>{var _,b;(b=(_=E.stacktrace)==null?void 0:_.frames)==null||b.forEach(T=>{T.filename&&(T.debug_id=g[T.filename])})})}(c,t.stackParser);const l=function(p,m){if(!m)return p;const g=p?p.clone():new Te;return g.update(m),g}(r,n.captureContext);n.mechanism&&ct(c,n.mechanism);const h=s?s.getEventProcessors():[],f=cn("globalScope",()=>new Te).getScopeData();o&&_s(f,o.getScopeData()),l&&_s(f,l.getScopeData());const d=[...n.attachments||[],...f.attachments];return d.length&&(n.attachments=d),ha(c,f),lr([...h,...f.eventProcessors],c,n).then(p=>(p&&function(m){var y,E;const g={};if((E=(y=m.exception)==null?void 0:y.values)==null||E.forEach(_=>{var b,T;(T=(b=_.stacktrace)==null?void 0:b.frames)==null||T.forEach(x=>{x.debug_id&&(x.abs_path?g[x.abs_path]=x.debug_id:x.filename&&(g[x.filename]=x.debug_id),delete x.debug_id)})}),Object.keys(g).length===0)return;m.debug_meta=m.debug_meta||{},m.debug_meta.images=m.debug_meta.images||[];const v=m.debug_meta.images;Object.entries(g).forEach(([_,b])=>{v.push({type:"sourcemap",code_file:_,debug_id:b})})}(p),typeof i=="number"&&i>0?function(m,g,v){var E,_;if(!m)return null;const y={...m,...m.breadcrumbs&&{breadcrumbs:m.breadcrumbs.map(b=>({...b,...b.data&&{data:ge(b.data,g,v)}}))},...m.user&&{user:ge(m.user,g,v)},...m.contexts&&{contexts:ge(m.contexts,g,v)},...m.extra&&{extra:ge(m.extra,g,v)}};return(E=m.contexts)!=null&&E.trace&&y.contexts&&(y.contexts.trace=m.contexts.trace,m.contexts.trace.data&&(y.contexts.trace.data=ge(m.contexts.trace.data,g,v))),m.spans&&(y.spans=m.spans.map(b=>({...b,...b.data&&{data:ge(b.data,g,v)}}))),(_=m.contexts)!=null&&_.flags&&y.contexts&&(y.contexts.flags=ge(m.contexts.flags,3,v)),y}(p,i,a):p))}function pr(t,e){return q().captureException(t,void 0)}function vs(t,e){return q().captureEvent(t,e)}function ys(t){const e=qe(),n=q(),{userAgent:r}=N.navigator||{},s=qi({user:n.getUser()||e.getUser(),...r&&{userAgent:r},...t}),o=e.getSession();return(o==null?void 0:o.status)==="ok"&&ut(o,{status:"exited"}),Yo(),e.setSession(s),s}function Yo(){const t=qe(),e=q().getSession()||t.getSession();e&&function(n,r){let s={};n.status==="ok"&&(s={status:"exited"}),ut(n,s)}(e),Ko(),t.setSession()}function Ko(){const t=qe(),e=j(),n=t.getSession();n&&e&&e.captureSession(n)}function bs(t=!1){t?Yo():Ko()}const ga="7";function _a(t,e,n){return e||`${function(r){return`${function(s){const o=s.protocol?`${s.protocol}:`:"",i=s.port?`:${s.port}`:"";return`${o}//${s.host}${i}${s.path?`/${s.path}`:""}/api/`}(r)}${r.projectId}/envelope/`}(t)}?${function(r,s){const o={sentry_version:ga};return r.publicKey&&(o.sentry_key=r.publicKey),s&&(o.sentry_client=`${s.name}/${s.version}`),new URLSearchParams(o).toString()}(t,n)}`}const Ss=[];function va(t){const e=t.defaultIntegrations||[],n=t.integrations;let r;if(e.forEach(s=>{s.isDefaultInstance=!0}),Array.isArray(n))r=[...e,...n];else if(typeof n=="function"){const s=n(e);r=Array.isArray(s)?s:[s]}else r=e;return function(s){const o={};return s.forEach(i=>{const{name:a}=i,c=o[a];c&&!c.isDefaultInstance&&i.isDefaultInstance||(o[a]=i)}),Object.values(o)}(r)}function ws(t,e){for(const n of e)n!=null&&n.afterAllSetup&&n.afterAllSetup(t)}function Es(t,e,n){if(n[e.name])$&&w.log(`Integration skipped because it was already installed: ${e.name}`);else{if(n[e.name]=e,Ss.indexOf(e.name)===-1&&typeof e.setupOnce=="function"&&(e.setupOnce(),Ss.push(e.name)),e.setup&&typeof e.setup=="function"&&e.setup(t),typeof e.preprocessEvent=="function"){const r=e.preprocessEvent.bind(e);t.on("preprocessEvent",(s,o)=>r(s,o,t))}if(typeof e.processEvent=="function"){const r=e.processEvent.bind(e),s=Object.assign((o,i)=>r(o,i,t),{id:e.name});t.addEventProcessor(s)}$&&w.log(`Integration installed: ${e.name}`)}}function Xo(t){const e=[];t.message&&e.push(t.message);try{const n=t.exception.values[t.exception.values.length-1];n!=null&&n.value&&(e.push(n.value),n.type&&e.push(`${n.type}: ${n.value}`))}catch{}return e}const xs="Not capturing exception because it's already been captured.",Ts="Discarded session because of missing or non-string release",Zo=Symbol.for("SentryInternalError"),Qo=Symbol.for("SentryDoNotSendEventError");function Vt(t){return{message:t,[Zo]:!0}}function qn(t){return{message:t,[Qo]:!0}}function ks(t){return!!t&&typeof t=="object"&&Zo in t}function $s(t){return!!t&&typeof t=="object"&&Qo in t}class ya{constructor(e){if(this._options=e,this._integrations={},this._numProcessing=0,this._outcomes={},this._hooks={},this._eventProcessors=[],e.dsn?this._dsn=ra(e.dsn):$&&w.warn("No DSN provided, client will not send events."),this._dsn){const n=_a(this._dsn,e.tunnel,e._metadata?e._metadata.sdk:void 0);this._transport=e.transport({tunnel:this._options.tunnel,recordDroppedEvent:this.recordDroppedEvent.bind(this),...e.transportOptions,url:n})}}captureException(e,n,r){const s=de();if(Gr(e))return $&&w.log(xs),s;const o={event_id:s,...n};return this._process(this.eventFromException(e,o).then(i=>this._captureEvent(i,o,r))),o.event_id}captureMessage(e,n,r,s){const o={event_id:de(),...r},i=xr(e)?e:String(e),a=Tt(e)?this.eventFromMessage(i,n,o):this.eventFromException(e,o);return this._process(a.then(c=>this._captureEvent(c,o,s))),o.event_id}captureEvent(e,n,r){const s=de();if(n!=null&&n.originalException&&Gr(n.originalException))return $&&w.log(xs),s;const o={event_id:s,...n},i=e.sdkProcessingMetadata||{},a=i.capturedSpanScope,c=i.capturedSpanIsolationScope;return this._process(this._captureEvent(e,o,a||r,c)),o.event_id}captureSession(e){this.sendSession(e),ut(e,{init:!1})}getDsn(){return this._dsn}getOptions(){return this._options}getSdkMetadata(){return this._options._metadata}getTransport(){return this._transport}flush(e){const n=this._transport;return n?(this.emit("flush"),this._isClientDoneProcessing(e).then(r=>n.flush(e).then(s=>r&&s))):et(!0)}close(e){return this.flush(e).then(n=>(this.getOptions().enabled=!1,this.emit("close"),n))}getEventProcessors(){return this._eventProcessors}addEventProcessor(e){this._eventProcessors.push(e)}init(){(this._isEnabled()||this._options.integrations.some(({name:e})=>e.startsWith("Spotlight")))&&this._setupIntegrations()}getIntegrationByName(e){return this._integrations[e]}addIntegration(e){const n=this._integrations[e.name];Es(this,e,this._integrations),n||ws(this,[e])}sendEvent(e,n={}){this.emit("beforeSendEvent",e,n);let r=ua(e,this._dsn,this._options._metadata,this._options.tunnel);for(const o of n.attachments||[])r=sa(r,aa(o));const s=this.sendEnvelope(r);s&&s.then(o=>this.emit("afterSendEvent",e,o),null)}sendSession(e){const{release:n,environment:r=Pr}=this._options;if("aggregates"in e){const o=e.attrs||{};if(!o.release&&!n)return void($&&w.warn(Ts));o.release=o.release||n,o.environment=o.environment||r,e.attrs=o}else{if(!e.release&&!n)return void($&&w.warn(Ts));e.release=e.release||n,e.environment=e.environment||r}this.emit("beforeSendSession",e);const s=function(o,i,a,c){const u=Wo(a);return lt({sent_at:new Date().toISOString(),...u&&{sdk:u},...!!c&&i&&{dsn:At(i)}},["aggregates"in o?[{type:"sessions"},o]:[{type:"session"},o.toJSON()]])}(e,this._dsn,this._options._metadata,this._options.tunnel);this.sendEnvelope(s)}recordDroppedEvent(e,n,r=1){if(this._options.sendClientReports){const s=`${e}:${n}`;$&&w.log(`Recording outcome: "${s}"${r>1?` (${r} times)`:""}`),this._outcomes[s]=(this._outcomes[s]||0)+r}}on(e,n){const r=this._hooks[e]=this._hooks[e]||[];return r.push(n),()=>{const s=r.indexOf(n);s>-1&&r.splice(s,1)}}emit(e,...n){const r=this._hooks[e];r&&r.forEach(s=>s(...n))}sendEnvelope(e){return this.emit("beforeEnvelope",e),this._isEnabled()&&this._transport?this._transport.send(e).then(null,n=>($&&w.error("Error while sending envelope:",n),n)):($&&w.error("Transport disabled"),et({}))}_setupIntegrations(){const{integrations:e}=this._options;this._integrations=function(n,r){const s={};return r.forEach(o=>{o&&Es(n,o,s)}),s}(this,e),ws(this,e)}_updateSessionFromEvent(e,n){var a;let r=n.level==="fatal",s=!1;const o=(a=n.exception)==null?void 0:a.values;if(o){s=!0;for(const c of o){const u=c.mechanism;if((u==null?void 0:u.handled)===!1){r=!0;break}}}const i=e.status==="ok";(i&&e.errors===0||i&&r)&&(ut(e,{...r&&{status:"crashed"},errors:e.errors||Number(s||r)}),this.captureSession(e))}_isClientDoneProcessing(e){return new Ne(n=>{let r=0;const s=setInterval(()=>{this._numProcessing==0?(clearInterval(s),n(!0)):(r+=1,e&&r>=e&&(clearInterval(s),n(!1)))},1)})}_isEnabled(){return this.getOptions().enabled!==!1&&this._transport!==void 0}_prepareEvent(e,n,r,s){const o=this.getOptions(),i=Object.keys(this._integrations);return!n.integrations&&(i!=null&&i.length)&&(n.integrations=i),this.emit("preprocessEvent",e,n),e.type||s.setLastEventId(e.event_id||n.event_id),ma(o,e,n,r,this,s).then(a=>{if(a===null)return a;this.emit("postprocessEvent",a,n),a.contexts={trace:Hi(r),...a.contexts};const c=Uo(this,r);return a.sdkProcessingMetadata={dynamicSamplingContext:c,...a.sdkProcessingMetadata},a})}_captureEvent(e,n={},r=q(),s=qe()){return $&&Bn(e)&&w.log(`Captured error event \`${Xo(e)[0]||"<unknown>"}\``),this._processEvent(e,n,r,s).then(o=>o.event_id,o=>{$&&($s(o)?w.log(o.message):ks(o)?w.warn(o.message):w.warn(o))})}_processEvent(e,n,r,s){const o=this.getOptions(),{sampleRate:i}=o,a=Is(e),c=Bn(e),u=e.type||"error",l=`before send for type \`${u}\``,h=i===void 0?void 0:Pt(i);if(c&&typeof h=="number"&&Math.random()>h)return this.recordDroppedEvent("sample_rate","error"),hn(qn(`Discarding event because it's not included in the random sample (sampling rate = ${i})`));const f=u==="replay_event"?"replay":u;return this._prepareEvent(e,n,r,s).then(d=>{if(d===null)throw this.recordDroppedEvent("event_processor",f),qn("An event processor returned `null`, will not send event.");if(n.data&&n.data.__sentry__===!0)return d;const p=function(m,g,v,y){const{beforeSend:E,beforeSendTransaction:_,beforeSendSpan:b}=g;let T=v;if(Bn(T)&&E)return E(T,y);if(Is(T)){if(b){const D=b(function(S){var G;const{trace_id:O,parent_span_id:R,span_id:k,status:C,origin:P,data:I,op:M}=((G=S.contexts)==null?void 0:G.trace)??{};return{data:I??{},description:S.transaction,op:M,parent_span_id:R,span_id:k??"",start_timestamp:S.start_timestamp??0,status:C,timestamp:S.timestamp,trace_id:O??"",origin:P,profile_id:I==null?void 0:I[or],exclusive_time:I==null?void 0:I[Ct],measurements:S.measurements,is_segment:!0}}(T));if(D?T=qt(v,{type:"transaction",timestamp:(x=D).timestamp,start_timestamp:x.start_timestamp,transaction:x.description,contexts:{trace:{trace_id:x.trace_id,span_id:x.span_id,parent_span_id:x.parent_span_id,op:x.op,status:x.status,origin:x.origin,data:{...x.data,...x.profile_id&&{[or]:x.profile_id},...x.exclusive_time&&{[Ct]:x.exclusive_time}}}},measurements:x.measurements}):ar(),T.spans){const S=[];for(const O of T.spans){const R=b(O);R?S.push(R):(ar(),S.push(O))}T.spans=S}}if(_){if(T.spans){const D=T.spans.length;T.sdkProcessingMetadata={...v.sdkProcessingMetadata,spanCountBeforeProcessing:D}}return _(T,y)}}var x;return T}(0,o,d,n);return function(m,g){const v=`${g} must return \`null\` or a valid event.`;if(Tn(m))return m.then(y=>{if(!kt(y)&&y!==null)throw Vt(v);return y},y=>{throw Vt(`${g} rejected with ${y}`)});if(!kt(m)&&m!==null)throw Vt(v);return m}(p,l)}).then(d=>{var g;if(d===null){if(this.recordDroppedEvent("before_send",f),a){const v=1+(e.spans||[]).length;this.recordDroppedEvent("before_send","span",v)}throw qn(`${l} returned \`null\`, will not send event.`)}const p=r.getSession()||s.getSession();if(c&&p&&this._updateSessionFromEvent(p,d),a){const v=(((g=d.sdkProcessingMetadata)==null?void 0:g.spanCountBeforeProcessing)||0)-(d.spans?d.spans.length:0);v>0&&this.recordDroppedEvent("before_send","span",v)}const m=d.transaction_info;if(a&&m&&d.transaction!==e.transaction){const v="custom";d.transaction_info={...m,source:v}}return this.sendEvent(d,n),d}).then(null,d=>{throw $s(d)||ks(d)?d:(this.captureException(d,{data:{__sentry__:!0},originalException:d}),Vt(`Event processing pipeline threw an error, original event will not be sent. Details have been sent as a new event.
Reason: ${d}`))})}_process(e){this._numProcessing++,e.then(n=>(this._numProcessing--,n),n=>(this._numProcessing--,n))}_clearOutcomes(){const e=this._outcomes;return this._outcomes={},Object.entries(e).map(([n,r])=>{const[s,o]=n.split(":");return{reason:s,category:o,quantity:r}})}_flushOutcomes(){$&&w.log("Flushing outcomes...");const e=this._clearOutcomes();if(e.length===0)return void($&&w.log("No outcomes to send"));if(!this._dsn)return void($&&w.log("No dsn provided, will not send outcomes"));$&&w.log("Sending outcomes:",e);const n=(r=e,lt((s=this._options.tunnel&&At(this._dsn))?{dsn:s}:{},[[{type:"client_report"},{timestamp:Ft(),discarded_events:r}]]));var r,s;this.sendEnvelope(n)}}function Bn(t){return t.type===void 0}function Is(t){return t.type==="transaction"}function zn(t,e){var o;const n=function(i){var a;return(a=N._sentryClientToLogBufferMap)==null?void 0:a.get(i)}(t)??[];if(n.length===0)return;const r=t.getOptions(),s=function(i,a,c,u){const l={};return a!=null&&a.sdk&&(l.sdk={name:a.sdk.name,version:a.sdk.version}),c&&u&&(l.dsn=At(u)),lt(l,[(h=i,[{type:"log",item_count:h.length,content_type:"application/vnd.sentry.items.log+json"},{items:h}])]);var h}(n,r._metadata,r.tunnel,t.getDsn());(o=N._sentryClientToLogBufferMap)==null||o.set(t,[]),t.emit("flushLogs"),t.sendEnvelope(s)}function ba(t,e){e.debug===!0&&($?w.enable():rt(()=>{console.warn("[Sentry] Cannot initialize SDK with `debug` option using a non-debug bundle.")})),q().update(e.initialScope);const n=new t(e);return function(r){q().setClient(r)}(n),n.init(),n}N._sentryClientToLogBufferMap=new WeakMap;const ei=Symbol.for("SentryBufferFullError");function Sa(t){const e=[];function n(r){return e.splice(e.indexOf(r),1)[0]||Promise.resolve(void 0)}return{$:e,add:function(r){if(!(t===void 0||e.length<t))return hn(ei);const s=r();return e.indexOf(s)===-1&&e.push(s),s.then(()=>n(s)).then(null,()=>n(s).then(null,()=>{})),s},drain:function(r){return new Ne((s,o)=>{let i=e.length;if(!i)return s(!0);const a=setTimeout(()=>{r&&r>0&&s(!1)},r);e.forEach(c=>{et(c).then(()=>{--i||(clearTimeout(a),s(!0))},o)})})}}}const wa=6e4;function Ea(t,{statusCode:e,headers:n},r=Date.now()){const s={...t},o=n==null?void 0:n["x-sentry-rate-limits"],i=n==null?void 0:n["retry-after"];if(o)for(const a of o.trim().split(",")){const[c,u,,,l]=a.split(":",5),h=parseInt(c,10),f=1e3*(isNaN(h)?60:h);if(u)for(const d of u.split(";"))d==="metric_bucket"&&l&&!l.split(";").includes("custom")||(s[d]=r+f);else s.all=r+f}else i?s.all=r+function(a,c=Date.now()){const u=parseInt(`${a}`,10);if(!isNaN(u))return 1e3*u;const l=Date.parse(`${a}`);return isNaN(l)?wa:l-c}(i,r):e===429&&(s.all=r+6e4);return s}const xa=64;function Ta(t,e,n=Sa(t.bufferSize||xa)){let r={};return{send:function(s){const o=[];if(us(s,(c,u)=>{const l=ds(u);(function(h,f,d=Date.now()){return function(p,m){return p[m]||p.all||0}(h,f)>d})(r,l)?t.recordDroppedEvent("ratelimit_backoff",l):o.push(c)}),o.length===0)return et({});const i=lt(s[0],o),a=c=>{us(i,(u,l)=>{t.recordDroppedEvent(c,ds(l))})};return n.add(()=>e({body:oa(i)}).then(c=>(c.statusCode!==void 0&&(c.statusCode<200||c.statusCode>=300)&&$&&w.warn(`Sentry responded with status code ${c.statusCode} to sent event.`),r=Ea(r,c),c),c=>{throw a("network_error"),$&&w.error("Encountered error running transport request:",c),c})).then(c=>c,c=>{if(c===ei)return $&&w.error("Skipped sending event because buffer is full."),a("queue_overflow"),et({});throw c})},flush:s=>n.drain(s)}}function ka(t){var e;((e=t.user)==null?void 0:e.ip_address)===void 0&&(t.user={...t.user,ip_address:"{{auto}}"})}function $a(t){var e;"aggregates"in t?((e=t.attrs)==null?void 0:e.ip_address)===void 0&&(t.attrs={...t.attrs,ip_address:"{{auto}}"}):t.ipAddress===void 0&&(t.ipAddress="{{auto}}")}function ti(t,e,n=[e],r="npm"){const s=t._metadata||{};s.sdk||(s.sdk={name:`sentry.javascript.${e}`,packages:n.map(o=>({name:`${r}:@sentry/${o}`,version:Je})),version:Je}),t._metadata=s}function ni(t={}){const e=j();if(!function(){const a=j();return(a==null?void 0:a.getOptions().enabled)!==!1&&!!(a!=null&&a.getTransport())}()||!e)return{};const n=_t(nt());if(n.getTraceData)return n.getTraceData(t);const r=q(),s=t.span||se(),o=s?function(a){const{traceId:c,spanId:u}=a.spanContext();return Xr(c,u,Ve(a))}(s):function(a){const{traceId:c,sampled:u,propagationSpanId:l}=a.getPropagationContext();return Xr(c,l,u)}(r),i=Yi(s?Le(s):Uo(e,r));return Lo.test(o)?{"sentry-trace":o,baggage:i}:(w.warn("Invalid sentry-trace data. Cannot generate trace data"),{})}const Ia=100;function ze(t,e){const n=j(),r=qe();if(!n)return;const{beforeBreadcrumb:s=null,maxBreadcrumbs:o=Ia}=n.getOptions();if(o<=0)return;const i={timestamp:Ft(),...t},a=s?rt(()=>s(i,e)):i;a!==null&&(n.emit&&n.emit("beforeAddBreadcrumb",a,e),r.addBreadcrumb(a,o))}let Cs;const Ps=new WeakMap,Ca=()=>({name:"FunctionToString",setupOnce(){Cs=Function.prototype.toString;try{Function.prototype.toString=function(...t){const e=kr(this),n=Ps.has(j())&&e!==void 0?e:this;return Cs.apply(n,t)}}catch{}},setup(t){Ps.set(t,!0)}}),Pa=[/^Script error\.?$/,/^Javascript error: Script error\.? on line 0$/,/^ResizeObserver loop completed with undelivered notifications.$/,/^Cannot redefine property: googletag$/,/^Can't find variable: gmo$/,/^undefined is not an object \(evaluating 'a\.[A-Z]'\)$/,`can't redefine non-configurable property "solana"`,"vv().getRestrictions is not a function. (In 'vv().getRestrictions(1,a)', 'vv().getRestrictions' is undefined)","Can't find variable: _AutofillCallbackHandler",/^Non-Error promise rejection captured with value: Object Not Found Matching Id:\d+, MethodName:simulateEvent, ParamCount:\d+$/,/^Java exception was raised during method invocation$/],Aa=(t={})=>{let e;return{name:"EventFilters",setup(n){const r=n.getOptions();e=As(t,r)},processEvent(n,r,s){if(!e){const o=s.getOptions();e=As(t,o)}return function(o,i){if(o.type){if(o.type==="transaction"&&function(a,c){if(!(c!=null&&c.length))return!1;const u=a.transaction;return!!u&&We(u,c)}(o,i.ignoreTransactions))return $&&w.warn(`Event dropped due to being matched by \`ignoreTransactions\` option.
Event: ${Ue(o)}`),!0}else{if(function(a,c){return c!=null&&c.length?Xo(a).some(u=>We(u,c)):!1}(o,i.ignoreErrors))return $&&w.warn(`Event dropped due to being matched by \`ignoreErrors\` option.
Event: ${Ue(o)}`),!0;if(function(a){var c,u;return(u=(c=a.exception)==null?void 0:c.values)!=null&&u.length?!a.message&&!a.exception.values.some(l=>l.stacktrace||l.type&&l.type!=="Error"||l.value):!1}(o))return $&&w.warn(`Event dropped due to not having an error message, error type or stacktrace.
Event: ${Ue(o)}`),!0;if(function(a,c){if(!(c!=null&&c.length))return!1;const u=Yt(a);return!!u&&We(u,c)}(o,i.denyUrls))return $&&w.warn(`Event dropped due to being matched by \`denyUrls\` option.
Event: ${Ue(o)}.
Url: ${Yt(o)}`),!0;if(!function(a,c){if(!(c!=null&&c.length))return!0;const u=Yt(a);return!u||We(u,c)}(o,i.allowUrls))return $&&w.warn(`Event dropped due to not being matched by \`allowUrls\` option.
Event: ${Ue(o)}.
Url: ${Yt(o)}`),!0}return!1}(n,e)?null:n}}},Oa=(t={})=>({...Aa(t),name:"InboundFilters"});function As(t={},e={}){return{allowUrls:[...t.allowUrls||[],...e.allowUrls||[]],denyUrls:[...t.denyUrls||[],...e.denyUrls||[]],ignoreErrors:[...t.ignoreErrors||[],...e.ignoreErrors||[],...t.disableErrorDefaults?[]:Pa],ignoreTransactions:[...t.ignoreTransactions||[],...e.ignoreTransactions||[]]}}function Yt(t){var e,n;try{const r=[...((e=t.exception)==null?void 0:e.values)??[]].reverse().find(o=>{var i,a,c;return((i=o.mechanism)==null?void 0:i.parent_id)===void 0&&((c=(a=o.stacktrace)==null?void 0:a.frames)==null?void 0:c.length)}),s=(n=r==null?void 0:r.stacktrace)==null?void 0:n.frames;return s?function(o=[]){for(let i=o.length-1;i>=0;i--){const a=o[i];if(a&&a.filename!=="<anonymous>"&&a.filename!=="[native code]")return a.filename||null}return null}(s):null}catch{return $&&w.error(`Cannot extract url for event ${Ue(t)}`),null}}function Ra(t,e,n,r,s,o){var a;if(!((a=s.exception)!=null&&a.values)||!o||!xe(o.originalException,Error))return;const i=s.exception.values.length>0?s.exception.values[s.exception.values.length-1]:void 0;i&&(s.exception.values=fr(t,e,r,o.originalException,n,s.exception.values,i,0))}function fr(t,e,n,r,s,o,i,a){if(o.length>=n+1)return o;let c=[...o];if(xe(r[s],Error)){Os(i,a);const u=t(e,r[s]),l=c.length;Rs(u,s,l,a),c=fr(t,e,n,r[s],s,[u,...c],u,l)}return Array.isArray(r.errors)&&r.errors.forEach((u,l)=>{if(xe(u,Error)){Os(i,a);const h=t(e,u),f=c.length;Rs(h,`errors[${l}]`,f,a),c=fr(t,e,n,u,s,[h,...c],h,f)}}),c}function Os(t,e){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,...t.type==="AggregateError"&&{is_exception_group:!0},exception_id:e}}function Rs(t,e,n,r){t.mechanism=t.mechanism||{type:"generic",handled:!0},t.mechanism={...t.mechanism,type:"chained",source:e,exception_id:n,parent_id:r}}function Da(){"console"in N&&nr.forEach(function(t){t in N.console&&oe(N.console,t,function(e){return un[t]=e,function(...n){le("console",{args:n,level:t});const r=un[t];r==null||r.apply(N.console,n)}})})}function La(t){return t==="warn"?"warning":["fatal","error","warning","log","info","debug"].includes(t)?t:"log"}const Na=()=>{let t;return{name:"Dedupe",processEvent(e){if(e.type)return e;try{if(function(n,r){return r?!!(function(s,o){const i=s.message,a=o.message;return!(!i&&!a||i&&!a||!i&&a||i!==a||!Ls(s,o)||!Ds(s,o))}(n,r)||function(s,o){const i=Ns(o),a=Ns(s);return!(!i||!a||i.type!==a.type||i.value!==a.value||!Ls(s,o)||!Ds(s,o))}(n,r)):!1}(e,t))return $&&w.warn("Event dropped due to being a duplicate of previously captured event."),null}catch{}return t=e}}};function Ds(t,e){let n=os(t),r=os(e);if(!n&&!r)return!0;if(n&&!r||!n&&r||r.length!==n.length)return!1;for(let s=0;s<r.length;s++){const o=r[s],i=n[s];if(o.filename!==i.filename||o.lineno!==i.lineno||o.colno!==i.colno||o.function!==i.function)return!1}return!0}function Ls(t,e){let n=t.fingerprint,r=e.fingerprint;if(!n&&!r)return!0;if(n&&!r||!n&&r)return!1;try{return n.join("")===r.join("")}catch{return!1}}function Ns(t){var e;return((e=t.exception)==null?void 0:e.values)&&t.exception.values[0]}const Ma="thismessage:/";function ri(t){return"isRelative"in t}function si(t,e){const n=t.indexOf("://")<=0&&t.indexOf("//")!==0,r=n?Ma:void 0;try{if("canParse"in URL&&!URL.canParse(t,r))return;const s=new URL(t,r);return n?{isRelative:n,pathname:s.pathname,search:s.search,hash:s.hash}:s}catch{}}function ja(t){if(ri(t))return t.pathname;const e=new URL(t);return e.search="",e.hash="",["80","443"].includes(e.port)&&(e.port=""),e.password&&(e.password="%filtered%"),e.username&&(e.username="%filtered%"),e.toString()}function it(t){if(!t)return{};const e=t.match(/^(([^:/?#]+):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?$/);if(!e)return{};const n=e[6]||"",r=e[8]||"";return{host:e[4],path:e[5],protocol:e[2],search:n,hash:r,relative:e[5]+n+r}}function Fa(t,e,n,r,s="auto.http.browser"){if(!t.fetchData)return;const{method:o,url:i}=t.fetchData,a=De()&&e(i);if(t.endTimestamp&&a){const h=t.fetchData.__span;if(!h)return;const f=r[h];return void(f&&(function(d,p){var m;if(p.response){Ao(d,p.response.status);const g=((m=p.response)==null?void 0:m.headers)&&p.response.headers.get("content-length");if(g){const v=parseInt(g);v>0&&d.setAttribute("http.response_content_length",v)}}else p.error&&d.setStatus({code:U,message:"internal_error"});d.end()}(f,t),delete r[h]))}const c=!!se(),u=a&&c?zt(function(h,f,d){const p=si(h);return{name:p?`${f} ${ja(p)}`:f,attributes:qa(h,p,f,d)}}(i,o,s)):new Xe;if(t.fetchData.__span=u.spanContext().spanId,r[u.spanContext().spanId]=u,n(t.fetchData.url)){const h=t.args[0],f=t.args[1]||{},d=function(p,m,g){const v=ni({span:g}),y=v["sentry-trace"],E=v.baggage;if(!y)return;const _=m.headers||(Eo(p)?p.headers:void 0);if(_){if(function(b){return typeof Headers<"u"&&xe(b,Headers)}(_)){const b=new Headers(_);if(b.get("sentry-trace")||b.set("sentry-trace",y),E){const T=b.get("baggage");T?Kt(T)||b.set("baggage",`${T},${E}`):b.set("baggage",E)}return b}if(Array.isArray(_)){const b=[..._];_.find(x=>x[0]==="sentry-trace")||b.push(["sentry-trace",y]);const T=_.find(x=>x[0]==="baggage"&&Kt(x[1]));return E&&!T&&b.push(["baggage",E]),b}{const b="sentry-trace"in _?_["sentry-trace"]:void 0,T="baggage"in _?_.baggage:void 0,x=T?Array.isArray(T)?[...T]:[T]:[],D=T&&(Array.isArray(T)?T.find(S=>Kt(S)):Kt(T));return E&&!D&&x.push(E),{..._,"sentry-trace":b??y,baggage:x.length>0?x.join(","):void 0}}}return{...v}}(h,f,De()&&c?u:void 0);d&&(t.args[1]=f,f.headers=d)}const l=j();if(l){const h={input:t.args,response:t.response,startTimestamp:t.startTimestamp,endTimestamp:t.endTimestamp};l.emit("beforeOutgoingRequestSpan",u,h)}return u}function Kt(t){return t.split(",").some(e=>e.trim().startsWith(Ir))}function qa(t,e,n,r){const s={url:t,type:"fetch","http.method":n,[W]:r,[Qe]:"http.client"};return e&&(ri(e)||(s["http.url"]=e.href,s["server.address"]=e.host),e.search&&(s["http.query"]=e.search),e.hash&&(s["http.fragment"]=e.hash)),s}function Ms(t){return t===void 0?void 0:t>=400&&t<500?"warning":t>=500?"error":void 0}const Ot=N;function oi(){if(!("fetch"in Ot))return!1;try{return new Headers,new Request("http://www.example.com"),new Response,!0}catch{return!1}}function hr(t){return t&&/^function\s+\w+\(\)\s+\{\s+\[native code\]\s+\}$/.test(t.toString())}function ii(t,e){const n="fetch";Oe(n,t),Re(n,()=>ai(void 0,e))}function ai(t,e=!1){e&&!function(){var s;if(typeof EdgeRuntime=="string")return!0;if(!oi())return!1;if(hr(Ot.fetch))return!0;let n=!1;const r=Ot.document;if(r&&typeof r.createElement=="function")try{const o=r.createElement("iframe");o.hidden=!0,r.head.appendChild(o),(s=o.contentWindow)!=null&&s.fetch&&(n=hr(o.contentWindow.fetch)),r.head.removeChild(o)}catch(o){$&&w.warn("Could not create sandbox iframe for pure fetch check, bailing to window.fetch: ",o)}return n}()||oe(N,"fetch",function(n){return function(...r){const s=new Error,{method:o,url:i}=function(c){if(c.length===0)return{method:"GET",url:""};if(c.length===2){const[l,h]=c;return{url:js(l),method:mr(h,"method")?String(h.method).toUpperCase():"GET"}}const u=c[0];return{url:js(u),method:mr(u,"method")?String(u.method).toUpperCase():"GET"}}(r),a={args:r,fetchData:{method:o,url:i},startTimestamp:1e3*te(),virtualError:s,headers:za(r)};return t||le("fetch",{...a}),n.apply(N,r).then(async c=>(t?t(c):le("fetch",{...a,endTimestamp:1e3*te(),response:c}),c),c=>{if(le("fetch",{...a,endTimestamp:1e3*te(),error:c}),Er(c)&&c.stack===void 0&&(c.stack=s.stack,ae(c,"framesToPop",1)),c instanceof TypeError&&(c.message==="Failed to fetch"||c.message==="Load failed"||c.message==="NetworkError when attempting to fetch resource."))try{const u=new URL(a.fetchData.url);c.message=`${c.message} (${u.host})`}catch{}throw c})}})}function Ba(t){let e;try{e=t.clone()}catch{return}(async function(n,r){if(n!=null&&n.body){const s=n.body,o=s.getReader(),i=setTimeout(()=>{s.cancel().then(null,()=>{})},9e4);let a=!0;for(;a;){let c;try{c=setTimeout(()=>{s.cancel().then(null,()=>{})},5e3);const{done:u}=await o.read();clearTimeout(c),u&&(r(),a=!1)}catch{a=!1}finally{clearTimeout(c)}}clearTimeout(i),o.releaseLock(),s.cancel().then(null,()=>{})}})(e,()=>{le("fetch-body-resolved",{endTimestamp:1e3*te(),response:t})})}function mr(t,e){return!!t&&typeof t=="object"&&!!t[e]}function js(t){return typeof t=="string"?t:t?mr(t,"url")?t.url:t.toString?t.toString():"":""}function za(t){const[e,n]=t;try{if(typeof n=="object"&&n!==null&&"headers"in n&&n.headers)return new Headers(n.headers);if(Eo(e))return new Headers(e.headers)}catch{}}const L=N;let gr=0;function Fs(){return gr>0}function pt(t,e={}){if(!function(r){return typeof r=="function"}(t))return t;try{const r=t.__sentry_wrapped__;if(r)return typeof r=="function"?r:t;if(kr(t))return t}catch{return t}const n=function(...r){try{const s=r.map(o=>pt(o,e));return t.apply(this,s)}catch(s){throw gr++,setTimeout(()=>{gr--}),It(o=>{o.addEventProcessor(i=>(e.mechanism&&(rr(i,void 0),ct(i,e.mechanism)),i.extra={...i.extra,arguments:r},i)),pr(s)}),s}};try{for(const r in t)Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r])}catch{}To(n,t),ae(t,"__sentry_wrapped__",n);try{Object.getOwnPropertyDescriptor(n,"name").configurable&&Object.defineProperty(n,"name",{get:()=>t.name})}catch{}return n}function _r(){const t=jt(),{referrer:e}=L.document||{},{userAgent:n}=L.navigator||{};return{url:t,headers:{...e&&{Referer:e},...n&&{"User-Agent":n}}}}function Ar(t,e){const n=Or(t,e),r={type:Wa(e),value:Ja(e)};return n.length&&(r.stacktrace={frames:n}),r.type===void 0&&r.value===""&&(r.value="Unrecoverable error caught"),r}function Ua(t,e,n,r){const s=j(),o=s==null?void 0:s.getOptions().normalizeDepth,i=function(u){for(const l in u)if(Object.prototype.hasOwnProperty.call(u,l)){const h=u[l];if(h instanceof Error)return h}}(e),a={__serialized__:Ho(e,o)};if(i)return{exception:{values:[Ar(t,i)]},extra:a};const c={exception:{values:[{type:xn(e)?e.constructor.name:r?"UnhandledRejection":"Error",value:Ga(e,{isUnhandledRejection:r})}]},extra:a};if(n){const u=Or(t,n);u.length&&(c.exception.values[0].stacktrace={frames:u})}return c}function Un(t,e){return{exception:{values:[Ar(t,e)]}}}function Or(t,e){const n=e.stacktrace||e.stack||"",r=function(o){return o&&Ha.test(o.message)?1:0}(e),s=function(o){return typeof o.framesToPop=="number"?o.framesToPop:0}(e);try{return t(n,r,s)}catch{}return[]}const Ha=/Minified React error #\d+;/i;function ci(t){return typeof WebAssembly<"u"&&WebAssembly.Exception!==void 0&&t instanceof WebAssembly.Exception}function Wa(t){const e=t==null?void 0:t.name;return!e&&ci(t)?t.message&&Array.isArray(t.message)&&t.message.length==2?t.message[0]:"WebAssembly.Exception":e}function Ja(t){const e=t==null?void 0:t.message;return ci(t)?Array.isArray(t.message)&&t.message.length==2?t.message[1]:"wasm exception":e?e.error&&typeof e.error.message=="string"?e.error.message:e:"No error message"}function vr(t,e,n,r,s){let o;if(So(e)&&e.error)return Un(t,e.error);if(Ur(e)||gt(e,"DOMException")){const i=e;if("stack"in e)o=Un(t,e);else{const a=i.name||(Ur(i)?"DOMError":"DOMException"),c=i.message?`${a}: ${i.message}`:a;o=yr(t,c,n,r),rr(o,c)}return"code"in i&&(o.tags={...o.tags,"DOMException.code":`${i.code}`}),o}return Er(e)?Un(t,e):kt(e)||xn(e)?(o=Ua(t,e,n,s),ct(o,{synthetic:!0}),o):(o=yr(t,e,n,r),rr(o,`${e}`),ct(o,{synthetic:!0}),o)}function yr(t,e,n,r){const s={};if(r&&n){const o=Or(t,n);o.length&&(s.exception={values:[{value:e,stacktrace:{frames:o}}]}),ct(s,{synthetic:!0})}if(xr(e)){const{__sentry_template_string__:o,__sentry_template_values__:i}=e;return s.logentry={message:o,params:i},s}return s.message=e,s}function Ga(t,{isUnhandledRejection:e}){const n=function(s,o=40){const i=Object.keys(ko(s));i.sort();const a=i[0];if(!a)return"[object has no keys]";if(a.length>=o)return dn(a,o);for(let c=i.length;c>0;c--){const u=i.slice(0,c).join(", ");if(!(u.length>o))return c===i.length?u:dn(u,o)}return""}(t),r=e?"promise rejection":"exception";return So(t)?`Event \`ErrorEvent\` captured as ${r} with message \`${t.message}\``:xn(t)?`Event \`${function(s){try{const o=Object.getPrototypeOf(s);return o?o.constructor.name:void 0}catch{}}(t)}\` (type=${t.type}) captured as ${r}`:`Object captured as ${r} with keys: ${n}`}class Va extends ya{constructor(e){const n={parentSpanIsAlwaysRootSpan:!0,...e};ti(n,"browser",["browser"],L.SENTRY_SDK_SOURCE||"npm"),super(n);const r=this,{sendDefaultPii:s,_experiments:o}=r._options,i=o==null?void 0:o.enableLogs;n.sendClientReports&&L.document&&L.document.addEventListener("visibilitychange",()=>{L.document.visibilityState==="hidden"&&(this._flushOutcomes(),i&&zn(r))}),i&&(r.on("flush",()=>{zn(r)}),r.on("afterCaptureLog",()=>{r._logFlushIdleTimeout&&clearTimeout(r._logFlushIdleTimeout),r._logFlushIdleTimeout=setTimeout(()=>{zn(r)},5e3)})),s&&(r.on("postprocessEvent",ka),r.on("beforeSendSession",$a))}eventFromException(e,n){return function(r,s,o,i){const a=vr(r,s,(o==null?void 0:o.syntheticException)||void 0,i);return ct(a),a.level="error",o!=null&&o.event_id&&(a.event_id=o.event_id),et(a)}(this._options.stackParser,e,n,this._options.attachStacktrace)}eventFromMessage(e,n="info",r){return function(s,o,i="info",a,c){const u=yr(s,o,(a==null?void 0:a.syntheticException)||void 0,c);return u.level=i,a!=null&&a.event_id&&(u.event_id=a.event_id),et(u)}(this._options.stackParser,e,n,r,this._options.attachStacktrace)}_prepareEvent(e,n,r,s){return e.platform=e.platform||"javascript",super._prepareEvent(e,n,r,s)}}const Rr=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,ft=(t,e,n,r)=>{let s,o;return i=>{e.value>=0&&(i||r)&&(o=e.value-(s||0),(o||s===void 0)&&(s=e.value,e.delta=o,e.rating=((a,c)=>a>c[1]?"poor":a>c[0]?"needs-improvement":"good")(e.value,n),t(e)))}},A=N,Rt=(t=!0)=>{var n,r;const e=(r=(n=A.performance)==null?void 0:n.getEntriesByType)==null?void 0:r.call(n,"navigation")[0];if(!t||e&&e.responseStart>0&&e.responseStart<performance.now())return e},Ut=()=>{const t=Rt();return(t==null?void 0:t.activationStart)||0},ht=(t,e)=>{var s,o;const n=Rt();let r="navigate";return n&&((s=A.document)!=null&&s.prerendering||Ut()>0?r="prerender":(o=A.document)!=null&&o.wasDiscarded?r="restore":n.type&&(r=n.type.replace(/_/g,"-"))),{name:t,value:e===void 0?-1:e,rating:"good",delta:0,entries:[],id:`v4-${Date.now()}-${Math.floor(8999999999999*Math.random())+1e12}`,navigationType:r}},tt=(t,e,n)=>{try{if(PerformanceObserver.supportedEntryTypes.includes(t)){const r=new PerformanceObserver(s=>{Promise.resolve().then(()=>{e(s.getEntries())})});return r.observe(Object.assign({type:t,buffered:!0},n||{})),r}}catch{}},vt=t=>{const e=n=>{var r;n.type!=="pagehide"&&((r=A.document)==null?void 0:r.visibilityState)!=="hidden"||t(n)};A.document&&(addEventListener("visibilitychange",e,!0),addEventListener("pagehide",e,!0))},Cn=t=>{let e=!1;return()=>{e||(t(),e=!0)}};let wt=-1;const mn=t=>{A.document.visibilityState==="hidden"&&wt>-1&&(wt=t.type==="visibilitychange"?t.timeStamp:0,Ya())},Ya=()=>{removeEventListener("visibilitychange",mn,!0),removeEventListener("prerenderingchange",mn,!0)},Pn=()=>(A.document&&wt<0&&(wt=A.document.visibilityState!=="hidden"||A.document.prerendering?1/0:0,addEventListener("visibilitychange",mn,!0),addEventListener("prerenderingchange",mn,!0)),{get firstHiddenTime(){return wt}}),Ht=t=>{var e;(e=A.document)!=null&&e.prerendering?addEventListener("prerenderingchange",()=>t(),!0):t()},Ka=[1800,3e3],Xa=[.1,.25],Za=(t,e={})=>{((n,r={})=>{Ht(()=>{const s=Pn(),o=ht("FCP");let i;const a=tt("paint",c=>{c.forEach(u=>{u.name==="first-contentful-paint"&&(a.disconnect(),u.startTime<s.firstHiddenTime&&(o.value=Math.max(u.startTime-Ut(),0),o.entries.push(u),i(!0)))})});a&&(i=ft(n,o,Ka,r.reportAllChanges))})})(Cn(()=>{const n=ht("CLS",0);let r,s=0,o=[];const i=c=>{c.forEach(u=>{if(!u.hadRecentInput){const l=o[0],h=o[o.length-1];s&&l&&h&&u.startTime-h.startTime<1e3&&u.startTime-l.startTime<5e3?(s+=u.value,o.push(u)):(s=u.value,o=[u])}}),s>n.value&&(n.value=s,n.entries=o,r())},a=tt("layout-shift",i);a&&(r=ft(t,n,Xa,e.reportAllChanges),vt(()=>{i(a.takeRecords()),r(!0)}),setTimeout(r,0))}))},Qa=[100,300],ec=(t,e={})=>{Ht(()=>{const n=Pn(),r=ht("FID");let s;const o=c=>{c.startTime<n.firstHiddenTime&&(r.value=c.processingStart-c.startTime,r.entries.push(c),s(!0))},i=c=>{c.forEach(o)},a=tt("first-input",i);s=ft(t,r,Qa,e.reportAllChanges),a&&vt(Cn(()=>{i(a.takeRecords()),a.disconnect()}))})};let ui=0,Hn=1/0,Xt=0;const tc=t=>{t.forEach(e=>{e.interactionId&&(Hn=Math.min(Hn,e.interactionId),Xt=Math.max(Xt,e.interactionId),ui=Xt?(Xt-Hn)/7+1:0)})};let br;const nc=()=>{"interactionCount"in performance||br||(br=tt("event",tc,{type:"event",buffered:!0,durationThreshold:0}))},ve=[],Wn=new Map,rc=()=>(br?ui:performance.interactionCount||0)-0,sc=[],oc=t=>{var r;if(sc.forEach(s=>s(t)),!t.interactionId&&t.entryType!=="first-input")return;const e=ve[ve.length-1],n=Wn.get(t.interactionId);if(n||ve.length<10||e&&t.duration>e.latency){if(n)t.duration>n.latency?(n.entries=[t],n.latency=t.duration):t.duration===n.latency&&t.startTime===((r=n.entries[0])==null?void 0:r.startTime)&&n.entries.push(t);else{const s={id:t.interactionId,latency:t.duration,entries:[t]};Wn.set(s.id,s),ve.push(s)}ve.sort((s,o)=>o.latency-s.latency),ve.length>10&&ve.splice(10).forEach(s=>Wn.delete(s.id))}},di=t=>{var r;const e=A.requestIdleCallback||A.setTimeout;let n=-1;return t=Cn(t),((r=A.document)==null?void 0:r.visibilityState)==="hidden"?t():(n=e(t),vt(t)),n},ic=[200,500],ac=(t,e={})=>{"PerformanceEventTiming"in A&&"interactionId"in PerformanceEventTiming.prototype&&Ht(()=>{nc();const n=ht("INP");let r;const s=i=>{di(()=>{i.forEach(oc);const a=(()=>{const c=Math.min(ve.length-1,Math.floor(rc()/50));return ve[c]})();a&&a.latency!==n.value&&(n.value=a.latency,n.entries=a.entries,r())})},o=tt("event",s,{durationThreshold:e.durationThreshold!=null?e.durationThreshold:40});r=ft(t,n,ic,e.reportAllChanges),o&&(o.observe({type:"first-input",buffered:!0}),vt(()=>{s(o.takeRecords()),r(!0)}))})},cc=[2500,4e3],qs={},uc=(t,e={})=>{Ht(()=>{const n=Pn(),r=ht("LCP");let s;const o=a=>{e.reportAllChanges||(a=a.slice(-1)),a.forEach(c=>{c.startTime<n.firstHiddenTime&&(r.value=Math.max(c.startTime-Ut(),0),r.entries=[c],s())})},i=tt("largest-contentful-paint",o);if(i){s=ft(t,r,cc,e.reportAllChanges);const a=Cn(()=>{qs[r.id]||(o(i.takeRecords()),i.disconnect(),qs[r.id]=!0,s(!0))});["keydown","click"].forEach(c=>{A.document&&addEventListener(c,()=>di(a),{once:!0,capture:!0})}),vt(a)}})},dc=[800,1800],Sr=t=>{var e,n;(e=A.document)!=null&&e.prerendering?Ht(()=>Sr(t)):((n=A.document)==null?void 0:n.readyState)!=="complete"?addEventListener("load",()=>Sr(t),!0):setTimeout(t,0)},lc=(t,e={})=>{const n=ht("TTFB"),r=ft(t,n,dc,e.reportAllChanges);Sr(()=>{const s=Rt();s&&(n.value=Math.max(s.responseStart-Ut(),0),n.entries=[s],r(!0))})},Et={},gn={};let li,pi,fi,hi,mi;function gi(t,e=!1){return xt("cls",t,pc,li,e)}function bt(t,e){return _i(t,e),gn[t]||(function(n){const r={};n==="event"&&(r.durationThreshold=0),tt(n,s=>{yt(n,{entries:s})},r)}(t),gn[t]=!0),vi(t,e)}function yt(t,e){const n=Et[t];if(n!=null&&n.length)for(const r of n)try{r(e)}catch(s){Rr&&w.error(`Error while triggering instrumentation handler.
Type: ${t}
Name: ${ke(r)}
Error:`,s)}}function pc(){return Za(t=>{yt("cls",{metric:t}),li=t},{reportAllChanges:!0})}function fc(){return ec(t=>{yt("fid",{metric:t}),pi=t})}function hc(){return uc(t=>{yt("lcp",{metric:t}),fi=t},{reportAllChanges:!0})}function mc(){return lc(t=>{yt("ttfb",{metric:t}),hi=t})}function gc(){return ac(t=>{yt("inp",{metric:t}),mi=t})}function xt(t,e,n,r,s=!1){let o;return _i(t,e),gn[t]||(o=n(),gn[t]=!0),r&&e({metric:r}),vi(t,e,s?o:void 0)}function _i(t,e){Et[t]=Et[t]||[],Et[t].push(e)}function vi(t,e,n){return()=>{n&&n();const r=Et[t];if(!r)return;const s=r.indexOf(e);s!==-1&&r.splice(s,1)}}function Jn(t){return typeof t=="number"&&isFinite(t)}function be(t,e,n,{...r}){const s=F(t).start_timestamp;return s&&s>e&&typeof t.updateStartTime=="function"&&t.updateStartTime(e),Go(t,()=>{const o=zt({startTime:e,...r});return o&&o.end(n),o})}function yi(t){var m;const e=j();if(!e)return;const{name:n,transaction:r,attributes:s,startTime:o}=t,{release:i,environment:a,sendDefaultPii:c}=e.getOptions(),u=e.getIntegrationByName("Replay"),l=u==null?void 0:u.getReplayId(),h=q(),f=h.getUser(),d=f!==void 0?f.email||f.id||f.ip_address:void 0;let p;try{p=h.getScopeData().contexts.profile.profile_id}catch{}return zt({name:n,attributes:{release:i,environment:a,user:d||void 0,profile_id:p||void 0,replay_id:l||void 0,transaction:r,"user_agent.original":(m=A.navigator)==null?void 0:m.userAgent,"client.address":c?"{{auto}}":void 0,...s},startTime:o,experimental:{standalone:!0}})}function Dr(){return A.addEventListener&&A.performance}function z(t){return t/1e3}function bi(t){let e="unknown",n="unknown",r="";for(const s of t){if(s==="/"){[e,n]=t.split("/");break}if(!isNaN(Number(s))){e=r==="h"?"http":r,n=t.split(r)[1];break}r+=s}return r===t&&(e=r),{name:e,version:n}}function _c(){let t,e,n=0;if(!function(){try{return PerformanceObserver.supportedEntryTypes.includes("layout-shift")}catch{return!1}}())return;let r=!1;function s(){r||(r=!0,e&&function(i,a,c){var p;Rr&&w.log(`Sending CLS span (${i})`);const u=z((ue()||0)+((a==null?void 0:a.startTime)||0)),l=q().getScopeData().transactionName,h=a?Ze((p=a.sources[0])==null?void 0:p.node):"Layout shift",f={[W]:"auto.http.browser.cls",[Qe]:"ui.webvital.cls",[Ct]:(a==null?void 0:a.duration)||0,"sentry.pageload.span_id":c},d=yi({name:h,transaction:l,attributes:f,startTime:u});d&&(d.addEvent("cls",{[kn]:"",[$n]:i}),d.end(u))}(n,t,e),o())}const o=gi(({metric:i})=>{const a=i.entries[i.entries.length-1];a&&(n=i.value,t=a)},!0);vt(()=>{s()}),setTimeout(()=>{const i=j();if(!i)return;const a=i.on("startNavigationSpan",()=>{s(),a==null||a()}),c=se();if(c){const u=ee(c);F(u).op==="pageload"&&(e=u.spanContext().spanId)}},0)}const vc=2147483647;let ne,st,Bs=0,V={};function yc({recordClsStandaloneSpans:t}){const e=Dr();if(e&&ue()){e.mark&&A.performance.mark("sentry-tracing-init");const n=xt("fid",({metric:i})=>{const a=i.entries[i.entries.length-1];if(!a)return;const c=z(ue()),u=z(a.startTime);V.fid={value:i.value,unit:"millisecond"},V["mark.fid"]={value:c+u,unit:"second"}},fc,pi),r=function(i,a=!1){return xt("lcp",i,hc,fi,a)}(({metric:i})=>{const a=i.entries[i.entries.length-1];a&&(V.lcp={value:i.value,unit:"millisecond"},ne=a)},!0),s=function(i){return xt("ttfb",i,mc,hi)}(({metric:i})=>{i.entries[i.entries.length-1]&&(V.ttfb={value:i.value,unit:"millisecond"})}),o=t?_c():gi(({metric:i})=>{const a=i.entries[i.entries.length-1];a&&(V.cls={value:i.value,unit:""},st=a)},!0);return()=>{n(),r(),s(),o==null||o()}}return()=>{}}function bc(t,e){const n=Dr(),r=ue();if(!(n!=null&&n.getEntries)||!r)return;const s=z(r),o=n.getEntries(),{op:i,start_timestamp:a}=F(t);if(o.slice(Bs).forEach(c=>{const u=z(c.startTime),l=z(Math.max(0,c.duration));if(!(i==="navigation"&&a&&s+u<a))switch(c.entryType){case"navigation":(function(h,f,d){["unloadEvent","redirect","domContentLoadedEvent","loadEvent","connect"].forEach(p=>{Zt(h,f,p,d)}),Zt(h,f,"secureConnection",d,"TLS/SSL"),Zt(h,f,"fetch",d,"cache"),Zt(h,f,"domainLookup",d,"DNS"),function(p,m,g){const v=g+z(m.requestStart),y=g+z(m.responseEnd),E=g+z(m.responseStart);m.responseEnd&&(be(p,v,y,{op:"browser.request",name:m.name,attributes:{[W]:"auto.ui.browser.metrics"}}),be(p,E,y,{op:"browser.response",name:m.name,attributes:{[W]:"auto.ui.browser.metrics"}}))}(h,f,d)})(t,c,s);break;case"mark":case"paint":case"measure":{(function(d,p,m,g,v){const y=Rt(!1),E=z(y?y.requestStart:0),_=v+Math.max(m,E),b=v+m,T=b+g,x={[W]:"auto.resource.browser.metrics"};if(_!==b&&(x["sentry.browser.measure_happened_before_request"]=!0,x["sentry.browser.measure_start_time"]=_),p.detail)if(typeof p.detail=="object")for(const[D,S]of Object.entries(p.detail))if(S&&Tt(S))x[`sentry.browser.measure.detail.${D}`]=S;else try{x[`sentry.browser.measure.detail.${D}`]=JSON.stringify(S)}catch{}else if(Tt(p.detail))x["sentry.browser.measure.detail"]=p.detail;else try{x["sentry.browser.measure.detail"]=JSON.stringify(p.detail)}catch{}_<=T&&be(d,_,T,{name:p.name,op:p.entryType,attributes:x})})(t,c,u,l,s);const h=Pn(),f=c.startTime<h.firstHiddenTime;c.name==="first-paint"&&f&&(V.fp={value:c.startTime,unit:"millisecond"}),c.name==="first-contentful-paint"&&f&&(V.fcp={value:c.startTime,unit:"millisecond"});break}case"resource":(function(h,f,d,p,m,g){if(f.initiatorType==="xmlhttprequest"||f.initiatorType==="fetch")return;const v=it(d),y={[W]:"auto.resource.browser.metrics"};Gn(y,f,"transferSize","http.response_transfer_size"),Gn(y,f,"encodedBodySize","http.response_content_length"),Gn(y,f,"decodedBodySize","http.decoded_response_content_length");const E=f.deliveryType;E!=null&&(y["http.response_delivery_type"]=E);const _=f.renderBlockingStatus;_&&(y["resource.render_blocking_status"]=_),v.protocol&&(y["url.scheme"]=v.protocol.split(":").pop()),v.host&&(y["server.address"]=v.host),y["url.same_origin"]=d.includes(A.location.origin);const{name:b,version:T}=bi(f.nextHopProtocol);y["network.protocol.name"]=b,y["network.protocol.version"]=T;const x=g+p,D=x+m;be(h,x,D,{name:d.replace(A.location.origin,""),op:f.initiatorType?`resource.${f.initiatorType}`:"resource.other",attributes:y})})(t,c,c.name,u,l,s)}}),Bs=Math.max(o.length-1,0),function(c){const u=A.navigator;if(!u)return;const l=u.connection;l&&(l.effectiveType&&c.setAttribute("effectiveConnectionType",l.effectiveType),l.type&&c.setAttribute("connectionType",l.type),Jn(l.rtt)&&(V["connection.rtt"]={value:l.rtt,unit:"millisecond"})),Jn(u.deviceMemory)&&c.setAttribute("deviceMemory",`${u.deviceMemory} GB`),Jn(u.hardwareConcurrency)&&c.setAttribute("hardwareConcurrency",String(u.hardwareConcurrency))}(t),i==="pageload"){(function(u){const l=Rt(!1);if(!l)return;const{responseStart:h,requestStart:f}=l;f<=h&&(u["ttfb.requestTime"]={value:h-f,unit:"millisecond"})})(V);const c=V["mark.fid"];c&&V.fid&&(be(t,c.value,c.value+z(V.fid.value),{name:"first input delay",op:"ui.action",attributes:{[W]:"auto.ui.browser.metrics"}}),delete V["mark.fid"]),"fcp"in V&&e.recordClsOnPageloadSpan||delete V.cls,Object.entries(V).forEach(([u,l])=>{(function(h,f,d,p=se()){const m=p&&ee(p);m&&($&&w.log(`[Measurement] Setting measurement on root span: ${h} = ${f} ${d}`),m.addEvent(h,{[$n]:f,[kn]:d}))})(u,l.value,l.unit)}),t.setAttribute("performance.timeOrigin",s),t.setAttribute("performance.activationStart",Ut()),function(u){ne&&(ne.element&&u.setAttribute("lcp.element",Ze(ne.element)),ne.id&&u.setAttribute("lcp.id",ne.id),ne.url&&u.setAttribute("lcp.url",ne.url.trim().slice(0,200)),ne.loadTime!=null&&u.setAttribute("lcp.loadTime",ne.loadTime),ne.renderTime!=null&&u.setAttribute("lcp.renderTime",ne.renderTime),u.setAttribute("lcp.size",ne.size)),st!=null&&st.sources&&st.sources.forEach((l,h)=>u.setAttribute(`cls.source.${h+1}`,Ze(l.node)))}(t)}ne=void 0,st=void 0,V={}}function Zt(t,e,n,r,s=n){const o=function(c){return c==="secureConnection"?"connectEnd":c==="fetch"?"domainLookupStart":`${c}End`}(n),i=e[o],a=e[`${n}Start`];a&&i&&be(t,r+z(a),r+z(i),{op:`browser.${s}`,name:e.name,attributes:{[W]:"auto.ui.browser.metrics",...n==="redirect"&&e.redirectCount!=null?{"http.redirect_count":e.redirectCount}:{}}})}function Gn(t,e,n,r){const s=e[n];s!=null&&s<vc&&(t[r]=s)}const Sc=1e3;let zs,Vn,Yn,Qt;function wc(){if(!A.document)return;const t=le.bind(null,"dom"),e=Us(t,!0);A.document.addEventListener("click",e,!1),A.document.addEventListener("keypress",e,!1),["EventTarget","Node"].forEach(n=>{var o,i;const r=A,s=(o=r[n])==null?void 0:o.prototype;(i=s==null?void 0:s.hasOwnProperty)!=null&&i.call(s,"addEventListener")&&(oe(s,"addEventListener",function(a){return function(c,u,l){if(c==="click"||c=="keypress")try{const h=this.__sentry_instrumentation_handlers__=this.__sentry_instrumentation_handlers__||{},f=h[c]=h[c]||{refCount:0};if(!f.handler){const d=Us(t);f.handler=d,a.call(this,c,d,l)}f.refCount++}catch{}return a.call(this,c,u,l)}}),oe(s,"removeEventListener",function(a){return function(c,u,l){if(c==="click"||c=="keypress")try{const h=this.__sentry_instrumentation_handlers__||{},f=h[c];f&&(f.refCount--,f.refCount<=0&&(a.call(this,c,f.handler,l),f.handler=void 0,delete h[c]),Object.keys(h).length===0&&delete this.__sentry_instrumentation_handlers__)}catch{}return a.call(this,c,u,l)}}))})}function Us(t,e=!1){return n=>{if(!n||n._sentryCaptured)return;const r=function(o){try{return o.target}catch{return null}}(n);if(function(o,i){return o==="keypress"&&(!(i!=null&&i.tagName)||i.tagName!=="INPUT"&&i.tagName!=="TEXTAREA"&&!i.isContentEditable)}(n.type,r))return;ae(n,"_sentryCaptured",!0),r&&!r._sentryId&&ae(r,"_sentryId",de());const s=n.type==="keypress"?"input":n.type;(function(o){if(o.type!==Vn)return!1;try{if(!o.target||o.target._sentryId!==Yn)return!1}catch{}return!0})(n)||(t({event:n,name:s,global:e}),Vn=n.type,Yn=r?r._sentryId:void 0),clearTimeout(zs),zs=A.setTimeout(()=>{Yn=void 0,Vn=void 0},Sc)}}function Lr(t){const e="history";Oe(e,t),Re(e,Ec)}function Ec(){function t(e){return function(...n){const r=n.length>2?n[2]:void 0;if(r){const s=Qt,o=function(i){try{return new URL(i,A.location.origin).toString()}catch{return i}}(String(r));if(Qt=o,s===o)return e.apply(this,n);le("history",{from:s,to:o})}return e.apply(this,n)}}A.addEventListener("popstate",()=>{const e=A.location.href,n=Qt;Qt=e,n!==e&&le("history",{from:n,to:e})}),"history"in Ot&&Ot.history&&(oe(A.history,"pushState",t),oe(A.history,"replaceState",t))}const on={};function Hs(t){on[t]=void 0}const ot="__sentry_xhr_v3__";function Si(t){Oe("xhr",t),Re("xhr",xc)}function xc(){if(!A.XMLHttpRequest)return;const t=XMLHttpRequest.prototype;t.open=new Proxy(t.open,{apply(e,n,r){const s=new Error,o=1e3*te(),i=Se(r[0])?r[0].toUpperCase():void 0,a=function(u){if(Se(u))return u;try{return u.toString()}catch{}}(r[1]);if(!i||!a)return e.apply(n,r);n[ot]={method:i,url:a,request_headers:{}},i==="POST"&&a.match(/sentry_key/)&&(n.__sentry_own_request__=!0);const c=()=>{const u=n[ot];if(u&&n.readyState===4){try{u.status_code=n.status}catch{}le("xhr",{endTimestamp:1e3*te(),startTimestamp:o,xhr:n,virtualError:s})}};return"onreadystatechange"in n&&typeof n.onreadystatechange=="function"?n.onreadystatechange=new Proxy(n.onreadystatechange,{apply:(u,l,h)=>(c(),u.apply(l,h))}):n.addEventListener("readystatechange",c),n.setRequestHeader=new Proxy(n.setRequestHeader,{apply(u,l,h){const[f,d]=h,p=l[ot];return p&&Se(f)&&Se(d)&&(p.request_headers[f.toLowerCase()]=d),u.apply(l,h)}}),e.apply(n,r)}}),t.send=new Proxy(t.send,{apply(e,n,r){const s=n[ot];return s?(r[0]!==void 0&&(s.body=r[0]),le("xhr",{startTimestamp:1e3*te(),xhr:n}),e.apply(n,r)):e.apply(n,r)}})}const Kn=[],an=new Map;function Tc(){if(Dr()&&ue()){const t=xt("inp",({metric:e})=>{if(e.value==null)return;const n=e.entries.find(f=>f.duration===e.value&&Ws[f.name]);if(!n)return;const{interactionId:r}=n,s=Ws[n.name],o=z(ue()+n.startTime),i=z(e.value),a=se(),c=a?ee(a):void 0,u=(r!=null?an.get(r):void 0)||c,l=u?F(u).description:q().getScopeData().transactionName,h=yi({name:Ze(n.target),transaction:l,attributes:{[W]:"auto.http.browser.inp",[Qe]:`ui.interaction.${s}`,[Ct]:n.duration},startTime:o});h&&(h.addEvent("inp",{[kn]:"millisecond",[$n]:e.value}),h.end(o+i))},gc,mi);return()=>{t()}}return()=>{}}const Ws={click:"click",pointerdown:"click",pointerup:"click",mousedown:"click",mouseup:"click",touchstart:"click",touchend:"click",mouseover:"hover",mouseout:"hover",mouseenter:"hover",mouseleave:"hover",pointerover:"hover",pointerout:"hover",pointerenter:"hover",pointerleave:"hover",dragstart:"drag",dragend:"drag",drag:"drag",dragenter:"drag",dragleave:"drag",dragover:"drag",drop:"drag",keydown:"press",keyup:"press",keypress:"press",input:"press"};function kc(t,e=function(n){const r=on[n];if(r)return r;let s=A[n];if(hr(s))return on[n]=s.bind(A);const o=A.document;if(o&&typeof o.createElement=="function")try{const i=o.createElement("iframe");i.hidden=!0,o.head.appendChild(i);const a=i.contentWindow;a!=null&&a[n]&&(s=a[n]),o.head.removeChild(i)}catch(i){Rr&&w.warn(`Could not create sandbox iframe for ${n} check, bailing to window.${n}: `,i)}return s&&(on[n]=s.bind(A))}("fetch")){let n=0,r=0;return Ta(t,function(s){const o=s.body.length;n+=o,r++;const i={body:s.body,method:"POST",referrerPolicy:"strict-origin",headers:t.headers,keepalive:n<=6e4&&r<15,...t.fetchOptions};if(!e)return Hs("fetch"),hn("No fetch implementation available");try{return e(t.url,i).then(a=>(n-=o,r--,{statusCode:a.status,headers:{"x-sentry-rate-limits":a.headers.get("X-Sentry-Rate-Limits"),"retry-after":a.headers.get("Retry-After")}}))}catch(a){return Hs("fetch"),n-=o,r--,hn(a)}})}function Xn(t,e,n,r){const s={filename:t,function:e==="<anonymous>"?Ke:e,in_app:!0};return n!==void 0&&(s.lineno=n),r!==void 0&&(s.colno=r),s}const $c=/^\s*at (\S+?)(?::(\d+))(?::(\d+))\s*$/i,Ic=/^\s*at (?:(.+?\)(?: \[.+\])?|.*?) ?\((?:address at )?)?(?:async )?((?:<anonymous>|[-a-z]+:|.*bundle|\/)?.*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Cc=/\((\S*)(?::(\d+))(?::(\d+))\)/,Pc=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)?((?:[-a-z]+)?:\/.*?|\[native code\]|[^@]*(?:bundle|\d+\.js)|\/[\w\-. /=]+)(?::(\d+))?(?::(\d+))?\s*$/i,Ac=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i,Oc=jo([30,t=>{const e=$c.exec(t);if(e){const[,r,s,o]=e;return Xn(r,Ke,+s,+o)}const n=Ic.exec(t);if(n){if(n[2]&&n[2].indexOf("eval")===0){const o=Cc.exec(n[2]);o&&(n[2]=o[1],n[3]=o[2],n[4]=o[3])}const[r,s]=Js(n[1]||Ke,n[2]);return Xn(s,r,n[3]?+n[3]:void 0,n[4]?+n[4]:void 0)}}],[50,t=>{const e=Pc.exec(t);if(e){if(e[3]&&e[3].indexOf(" > eval")>-1){const s=Ac.exec(e[3]);s&&(e[1]=e[1]||"eval",e[3]=s[1],e[4]=s[2],e[5]="")}let n=e[3],r=e[1]||Ke;return[r,n]=Js(r,n),Xn(n,r,e[4]?+e[4]:void 0,e[5]?+e[5]:void 0)}}]),Js=(t,e)=>{const n=t.indexOf("safari-extension")!==-1,r=t.indexOf("safari-web-extension")!==-1;return n||r?[t.indexOf("@")!==-1?t.split("@")[0]:Ke,n?`safari-extension:${e}`:`safari-web-extension:${e}`]:[t,e]},ce=typeof __SENTRY_DEBUG__>"u"||__SENTRY_DEBUG__,Gs=1024,Rc=(t={})=>{const e={console:!0,dom:!0,fetch:!0,history:!0,sentry:!0,xhr:!0,...t};return{name:"Breadcrumbs",setup(n){var r;e.console&&function(s){const o="console";Oe(o,s),Re(o,Da)}(function(s){return function(o){if(j()!==s)return;const i={category:"console",data:{arguments:o.args,logger:"console"},level:La(o.level),message:Hr(o.args," ")};if(o.level==="assert"){if(o.args[0]!==!1)return;i.message=`Assertion failed: ${Hr(o.args.slice(1)," ")||"console.assert"}`,i.data.arguments=o.args.slice(1)}ze(i,{input:o.args,level:o.level})}}(n)),e.dom&&(r=function(s,o){return function(i){if(j()!==s)return;let a,c,u=typeof o=="object"?o.serializeAttribute:void 0,l=typeof o=="object"&&typeof o.maxStringLength=="number"?o.maxStringLength:void 0;l&&l>Gs&&(ce&&w.warn(`\`dom.maxStringLength\` cannot exceed 1024, but a value of ${l} was configured. Sentry will use 1024 instead.`),l=Gs),typeof u=="string"&&(u=[u]);try{const f=i.event,d=function(p){return!!p&&!!p.target}(f)?f.target:f;a=Ze(d,{keyAttrs:u,maxStringLength:l}),c=xo(d)}catch{a="<unknown>"}if(a.length===0)return;const h={category:`ui.${i.name}`,message:a};c&&(h.data={"ui.component_name":c}),ze(h,{event:i.event,name:i.name,global:i.global})}}(n,e.dom),Oe("dom",r),Re("dom",wc)),e.xhr&&Si(function(s){return function(o){if(j()!==s)return;const{startTimestamp:i,endTimestamp:a}=o,c=o.xhr[ot];if(!i||!a||!c)return;const{method:u,url:l,status_code:h,body:f}=c,d={method:u,url:l,status_code:h},p={xhr:o.xhr,input:f,startTimestamp:i,endTimestamp:a},m={category:"xhr",data:d,type:"http",level:Ms(h)};s.emit("beforeOutgoingRequestBreadcrumb",m,p),ze(m,p)}}(n)),e.fetch&&ii(function(s){return function(o){if(j()!==s)return;const{startTimestamp:i,endTimestamp:a}=o;if(a&&(!o.fetchData.url.match(/sentry_key/)||o.fetchData.method!=="POST"))if(o.fetchData.method,o.fetchData.url,o.error){const c=o.fetchData,u={data:o.error,input:o.args,startTimestamp:i,endTimestamp:a},l={category:"fetch",data:c,level:"error",type:"http"};s.emit("beforeOutgoingRequestBreadcrumb",l,u),ze(l,u)}else{const c=o.response,u={...o.fetchData,status_code:c==null?void 0:c.status};o.fetchData.request_body_size,o.fetchData.response_body_size;const l={input:o.args,response:c,startTimestamp:i,endTimestamp:a},h={category:"fetch",data:u,type:"http",level:Ms(u.status_code)};s.emit("beforeOutgoingRequestBreadcrumb",h,l),ze(h,l)}}}(n)),e.history&&Lr(function(s){return function(o){if(j()!==s)return;let i=o.from,a=o.to;const c=it(L.location.href);let u=i?it(i):void 0;const l=it(a);u!=null&&u.path||(u=c),c.protocol===l.protocol&&c.host===l.host&&(a=l.relative),c.protocol===u.protocol&&c.host===u.host&&(i=u.relative),ze({category:"navigation",data:{from:i,to:a}})}}(n)),e.sentry&&n.on("beforeSendEvent",function(s){return function(o){j()===s&&ze({category:"sentry."+(o.type==="transaction"?"transaction":"event"),event_id:o.event_id,level:o.level,message:Ue(o)},{event:o})}}(n))}}},Dc=["EventTarget","Window","Node","ApplicationCache","AudioTrackList","BroadcastChannel","ChannelMergerNode","CryptoOperation","EventSource","FileReader","HTMLUnknownElement","IDBDatabase","IDBRequest","IDBTransaction","KeyOperation","MediaController","MessagePort","ModalWindow","Notification","SVGElementInstance","Screen","SharedWorker","TextTrack","TextTrackCue","TextTrackList","WebSocket","WebSocketWorker","Worker","XMLHttpRequest","XMLHttpRequestEventTarget","XMLHttpRequestUpload"],Lc=(t={})=>{const e={XMLHttpRequest:!0,eventTarget:!0,requestAnimationFrame:!0,setInterval:!0,setTimeout:!0,...t};return{name:"BrowserApiErrors",setupOnce(){e.setTimeout&&oe(L,"setTimeout",Vs),e.setInterval&&oe(L,"setInterval",Vs),e.requestAnimationFrame&&oe(L,"requestAnimationFrame",Nc),e.XMLHttpRequest&&"XMLHttpRequest"in L&&oe(XMLHttpRequest.prototype,"send",Mc);const n=e.eventTarget;n&&(Array.isArray(n)?n:Dc).forEach(jc)}}};function Vs(t){return function(...e){const n=e[0];return e[0]=pt(n,{mechanism:{data:{function:ke(t)},handled:!1,type:"instrument"}}),t.apply(this,e)}}function Nc(t){return function(e){return t.apply(this,[pt(e,{mechanism:{data:{function:"requestAnimationFrame",handler:ke(t)},handled:!1,type:"instrument"}})])}}function Mc(t){return function(...e){const n=this;return["onload","onerror","onprogress","onreadystatechange"].forEach(r=>{r in n&&typeof n[r]=="function"&&oe(n,r,function(s){const o={mechanism:{data:{function:r,handler:ke(s)},handled:!1,type:"instrument"}},i=kr(s);return i&&(o.mechanism.data.handler=ke(i)),pt(s,o)})}),t.apply(this,e)}}function jc(t){var r,s;const e=L,n=(r=e[t])==null?void 0:r.prototype;(s=n==null?void 0:n.hasOwnProperty)!=null&&s.call(n,"addEventListener")&&(oe(n,"addEventListener",function(o){return function(i,a,c){try{typeof a.handleEvent=="function"&&(a.handleEvent=pt(a.handleEvent,{mechanism:{data:{function:"handleEvent",handler:ke(a),target:t},handled:!1,type:"instrument"}}))}catch{}return o.apply(this,[i,pt(a,{mechanism:{data:{function:"addEventListener",handler:ke(a),target:t},handled:!1,type:"instrument"}}),c])}}),oe(n,"removeEventListener",function(o){return function(i,a,c){try{const u=a.__sentry_wrapped__;u&&o.call(this,i,u,c)}catch{}return o.call(this,i,a,c)}}))}const Fc=()=>({name:"BrowserSession",setupOnce(){L.document!==void 0?(ys({ignoreDuration:!0}),bs(),Lr(({from:t,to:e})=>{t!==void 0&&t!==e&&(ys({ignoreDuration:!0}),bs())})):ce&&w.warn("Using the `browserSessionIntegration` in non-browser environments is not supported.")}}),qc=(t={})=>{const e={onerror:!0,onunhandledrejection:!0,...t};return{name:"GlobalHandlers",setupOnce(){Error.stackTraceLimit=50},setup(n){e.onerror&&(function(r){Fo(s=>{const{stackParser:o,attachStacktrace:i}=Ks();if(j()!==r||Fs())return;const{msg:a,url:c,line:u,column:l,error:h}=s,f=function(d,p,m,g){const v=d.exception=d.exception||{},y=v.values=v.values||[],E=y[0]=y[0]||{},_=E.stacktrace=E.stacktrace||{},b=_.frames=_.frames||[],T=g,x=m,D=Se(p)&&p.length>0?p:jt();return b.length===0&&b.push({colno:T,filename:D,function:Ke,in_app:!0,lineno:x}),d}(vr(o,h||a,void 0,i,!1),c,u,l);f.level="error",vs(f,{originalException:h,mechanism:{handled:!1,type:"onerror"}})})}(n),Ys("onerror")),e.onunhandledrejection&&(function(r){qo(s=>{const{stackParser:o,attachStacktrace:i}=Ks();if(j()!==r||Fs())return;const a=function(u){if(Tt(u))return u;try{if("reason"in u)return u.reason;if("detail"in u&&"reason"in u.detail)return u.detail.reason}catch{}return u}(s),c=Tt(a)?{exception:{values:[{type:"UnhandledRejection",value:`Non-Error promise rejection captured with value: ${String(a)}`}]}}:vr(o,a,void 0,i,!0);c.level="error",vs(c,{originalException:a,mechanism:{handled:!1,type:"onunhandledrejection"}})})}(n),Ys("onunhandledrejection"))}}};function Ys(t){ce&&w.log(`Global Handler attached: ${t}`)}function Ks(){const t=j();return(t==null?void 0:t.getOptions())||{stackParser:()=>[],attachStacktrace:!1}}const Bc=()=>({name:"HttpContext",preprocessEvent(t){var r;if(!L.navigator&&!L.location&&!L.document)return;const e=_r(),n={...e.headers,...(r=t.request)==null?void 0:r.headers};t.request={...e,...t.request,headers:n}}}),zc=(t={})=>{const e=t.limit||5,n=t.key||"cause";return{name:"LinkedErrors",preprocessEvent(r,s,o){Ra(Ar,o.getOptions().stackParser,n,e,r,s)}}};function Uc(t){const e={};for(const n of Object.getOwnPropertyNames(t)){const r=n;t[r]!==void 0&&(e[r]=t[r])}return e}function Hc(t={}){const e=function(s={}){var o;return{defaultIntegrations:[Oa(),Ca(),Lc(),Rc(),qc(),zc(),Na(),Bc(),Fc()],release:typeof __SENTRY_RELEASE__=="string"?__SENTRY_RELEASE__:(o=L.SENTRY_RELEASE)==null?void 0:o.id,sendClientReports:!0,...Uc(s)}}(t);if(!e.skipBrowserExtensionCheck&&function(){var l;const s=L.window!==void 0&&L;if(!s)return!1;const o=s[s.chrome?"chrome":"browser"],i=(l=o==null?void 0:o.runtime)==null?void 0:l.id,a=jt()||"",c=!!i&&L===L.top&&["chrome-extension:","moz-extension:","ms-browser-extension:","safari-web-extension:"].some(h=>a.startsWith(`${h}//`)),u=s.nw!==void 0;return!!i&&!c&&!u}())return void(ce&&rt(()=>{console.error("[Sentry] You cannot run Sentry this way in a browser extension, check: https://docs.sentry.io/platforms/javascript/best-practices/browser-extensions/")}));ce&&!oi()&&w.warn("No Fetch API detected. The Sentry SDK requires a Fetch API compatible environment to send events. Please add a Fetch API polyfill.");const n={...e,stackParser:(r=e.stackParser||Oc,Array.isArray(r)?jo(...r):r),integrations:va(e),transport:e.transport||kc};var r;return ba(Va,n)}const Xs=new WeakMap,Zn=new Map,wi={traceFetch:!0,traceXHR:!0,enableHTTPTimings:!0,trackFetchStreamPerformance:!1};function Wc(t,e){const{traceFetch:n,traceXHR:r,trackFetchStreamPerformance:s,shouldCreateSpanForRequest:o,enableHTTPTimings:i,tracePropagationTargets:a,onRequestSpanStart:c}={...wi,...e},u=typeof o=="function"?o:f=>!0,l=f=>function(d,p){const m=jt();if(m){let g,v;try{g=new URL(d,m),v=new URL(m).origin}catch{return!1}const y=g.origin===v;return p?We(g.toString(),p)||y&&We(g.pathname,p):y}{const g=!!d.match(/^\/(?!\/)/);return p?We(d,p):g}}(f,a),h={};n&&(t.addEventProcessor(f=>(f.type==="transaction"&&f.spans&&f.spans.forEach(d=>{if(d.op==="http.client"){const p=Zn.get(d.span_id);p&&(d.timestamp=p/1e3,Zn.delete(d.span_id))}}),f)),s&&function(f){const d="fetch-body-resolved";Oe(d,f),Re(d,()=>ai(Ba))}(f=>{if(f.response){const d=Xs.get(f.response);d&&f.endTimestamp&&Zn.set(d,f.endTimestamp)}}),ii(f=>{const d=Fa(f,u,l,h);if(f.response&&f.fetchData.__span&&Xs.set(f.response,f.fetchData.__span),d){const p=Qs(f.fetchData.url),m=p?it(p).host:void 0;d.setAttributes({"http.url":p,"server.address":m}),i&&Zs(d),c==null||c(d,{headers:f.headers})}})),r&&Si(f=>{var p;const d=function(m,g,v,y){const E=m.xhr,_=E==null?void 0:E[ot];if(!E||E.__sentry_own_request__||!_)return;const{url:b,method:T}=_,x=De()&&g(b);if(m.endTimestamp&&x){const I=E.__sentry_xhr_span_id__;if(!I)return;const M=y[I];return void(M&&_.status_code!==void 0&&(Ao(M,_.status_code),M.end(),delete y[I]))}const D=Qs(b),S=it(D||b),O=(C=b,C.split(/[?#]/,1)[0]),R=!!se(),k=x&&R?zt({name:`${T} ${O}`,attributes:{url:b,type:"xhr","http.method":T,"http.url":D,"server.address":S==null?void 0:S.host,[W]:"auto.http.browser",[Qe]:"http.client",...(S==null?void 0:S.search)&&{"http.query":S==null?void 0:S.search},...(S==null?void 0:S.hash)&&{"http.fragment":S==null?void 0:S.hash}}}):new Xe;var C;E.__sentry_xhr_span_id__=k.spanContext().spanId,y[E.__sentry_xhr_span_id__]=k,v(b)&&function(I,M){const{"sentry-trace":G,baggage:Q}=ni({span:M});G&&function(Be,$e,Ie){var Nr;const pe=(Nr=Be.__sentry_xhr_v3__)==null?void 0:Nr.request_headers;if(!(pe!=null&&pe["sentry-trace"]))try{if(Be.setRequestHeader("sentry-trace",$e),Ie){const An=pe==null?void 0:pe.baggage;An&&An.split(",").some(ki=>ki.trim().startsWith("sentry-"))||Be.setRequestHeader("baggage",Ie)}}catch{}}(I,G,Q)}(E,De()&&R?k:void 0);const P=j();return P&&P.emit("beforeOutgoingRequestSpan",k,m),k}(f,u,l,h);if(d){let m;i&&Zs(d);try{m=new Headers((p=f.xhr.__sentry_xhr_v3__)==null?void 0:p.request_headers)}catch{}c==null||c(d,{headers:m})}})}function Zs(t){const{url:e}=F(t).data;if(!e||typeof e!="string")return;const n=bt("resource",({entries:r})=>{r.forEach(s=>{(function(o){return o.entryType==="resource"&&"initiatorType"in o&&typeof o.nextHopProtocol=="string"&&(o.initiatorType==="fetch"||o.initiatorType==="xmlhttprequest")})(s)&&s.name.endsWith(e)&&(function(o){const{name:i,version:a}=bi(o.nextHopProtocol),c=[];return c.push(["network.protocol.version",a],["network.protocol.name",i]),ue()?[...c,["http.request.redirect_start",he(o.redirectStart)],["http.request.fetch_start",he(o.fetchStart)],["http.request.domain_lookup_start",he(o.domainLookupStart)],["http.request.domain_lookup_end",he(o.domainLookupEnd)],["http.request.connect_start",he(o.connectStart)],["http.request.secure_connection_start",he(o.secureConnectionStart)],["http.request.connection_end",he(o.connectEnd)],["http.request.request_start",he(o.requestStart)],["http.request.response_start",he(o.responseStart)],["http.request.response_end",he(o.responseEnd)]]:c}(s).forEach(o=>t.setAttribute(...o)),setTimeout(n))})})}function he(t=0){return((ue()||performance.timeOrigin)+t)/1e3}function Qs(t){try{return new URL(t,L.location.origin).href}catch{return}}const Jc=3600,eo="sentry_previous_trace",Gc="sentry.previous_trace";function Vc(t,{linkPreviousTrace:e,consistentTraceSampling:n}){const r=e==="session-storage";let s=r?function(){var i;try{const a=(i=L.sessionStorage)==null?void 0:i.getItem(eo);return JSON.parse(a)}catch{return}}():void 0;t.on("spanStart",i=>{if(ee(i)!==i)return;const a=q().getPropagationContext();s=function(c,u,l){const h=F(u);function f(){var m,g;try{return Number((m=l.dsc)==null?void 0:m.sample_rate)??Number((g=h.data)==null?void 0:g[$r])}catch{return 0}}const d={spanContext:u.spanContext(),startTimestamp:h.start_timestamp,sampleRate:f(),sampleRand:l.sampleRand};if(!c)return d;const p=c.spanContext;return p.traceId===h.trace_id?c:(Date.now()/1e3-c.startTimestamp<=Jc&&(ce&&w.info(`Adding previous_trace ${p} link to span ${{op:h.op,...u.spanContext()}}`),u.addLink({context:p,attributes:{[Wi]:"previous_trace"}}),u.setAttribute(Gc,`${p.traceId}-${p.spanId}-${Qn(p)?1:0}`)),d)}(s,i,a),r&&function(c){try{L.sessionStorage.setItem(eo,JSON.stringify(c))}catch(u){ce&&w.warn("Could not store previous trace in sessionStorage",u)}}(s)});let o=!0;n&&t.on("beforeSampling",i=>{if(!s)return;const a=q(),c=a.getPropagationContext();o&&c.parentSpanId?o=!1:(a.setPropagationContext({...c,dsc:{...c.dsc,sample_rate:String(s.sampleRate),sampled:String(Qn(s.spanContext))},sampleRand:s.sampleRand}),i.parentSampled=Qn(s.spanContext),i.parentSampleRate=s.sampleRate,i.spanAttributes={...i.spanAttributes,[Co]:s.sampleRate})})}function Qn(t){return t.traceFlags===1}const Yc={...sn,instrumentNavigation:!0,instrumentPageLoad:!0,markBackgroundSpan:!0,enableLongTask:!0,enableLongAnimationFrame:!0,enableInp:!0,linkPreviousTrace:"in-memory",consistentTraceSampling:!1,_experiments:{},...wi};let to=!1;const Kc=(t={})=>{to&&rt(()=>{console.warn("Multiple browserTracingIntegration instances are not supported.")}),to=!0;const e=L.document;as||(as=!0,Fo(cr),qo(cr));const{enableInp:n,enableLongTask:r,enableLongAnimationFrame:s,_experiments:{enableInteractions:o,enableStandaloneClsSpans:i},beforeStartSpan:a,idleTimeout:c,finalTimeout:u,childSpanTimeout:l,markBackgroundSpan:h,traceFetch:f,traceXHR:d,trackFetchStreamPerformance:p,shouldCreateSpanForRequest:m,enableHTTPTimings:g,instrumentPageLoad:v,instrumentNavigation:y,linkPreviousTrace:E,consistentTraceSampling:_,onRequestSpanStart:b}={...Yc,...t},T=yc({recordClsStandaloneSpans:i||!1});n&&Tc(),s&&N.PerformanceObserver&&PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes("long-animation-frame")?new PerformanceObserver(S=>{const O=se();if(O)for(const R of S.getEntries()){if(!R.scripts[0])continue;const k=z(ue()+R.startTime),{start_timestamp:C,op:P}=F(O);if(P==="navigation"&&C&&k<C)continue;const I=z(R.duration),M={[W]:"auto.ui.browser.metrics"},G=R.scripts[0],{invoker:Q,invokerType:Be,sourceURL:$e,sourceFunctionName:Ie,sourceCharPosition:pe}=G;M["browser.script.invoker"]=Q,M["browser.script.invoker_type"]=Be,$e&&(M["code.filepath"]=$e),Ie&&(M["code.function"]=Ie),pe!==-1&&(M["browser.script.source_char_position"]=pe),be(O,k,k+I,{name:"Main UI thread blocked",op:"ui.long-animation-frame",attributes:M})}}).observe({type:"long-animation-frame",buffered:!0}):r&&bt("longtask",({entries:S})=>{const O=se();if(!O)return;const{op:R,start_timestamp:k}=F(O);for(const C of S){const P=z(ue()+C.startTime),I=z(C.duration);R==="navigation"&&k&&P<k||be(O,P,P+I,{name:"Main UI thread blocked",op:"ui.long-task",attributes:{[W]:"auto.ui.browser.metrics"}})}}),o&&bt("event",({entries:S})=>{const O=se();if(O){for(const R of S)if(R.name==="click"){const k=z(ue()+R.startTime),C=z(R.duration),P={name:Ze(R.target),op:`ui.interaction.${R.name}`,startTime:k,attributes:{[W]:"auto.ui.browser.metrics"}},I=xo(R.target);I&&(P.attributes["ui.component_name"]=I),be(O,k,k+C,P)}}});const x={name:void 0,source:void 0};function D(S,O){const R=O.op==="pageload",k=a?a(O):O,C=k.attributes||{};O.name!==k.name&&(C[ye]="custom",k.attributes=C),x.name=k.name,x.source=C[ye];const P=ms(k,{idleTimeout:c,finalTimeout:u,childSpanTimeout:l,disableAutoFinish:R,beforeSpanEnd:M=>{T(),bc(M,{recordClsOnPageloadSpan:!i}),ro(S,void 0);const G=q(),Q=G.getPropagationContext();G.setPropagationContext({...Q,traceId:P.spanContext().traceId,sampled:Ve(P),dsc:Le(M)})}});function I(){e&&["interactive","complete"].includes(e.readyState)&&S.emit("idleSpanEnableAutoFinish",P)}ro(S,P),R&&e&&(e.addEventListener("readystatechange",()=>{I()}),I())}return{name:"BrowserTracing",afterAllSetup(S){let O=jt();function R(){const k=en(S);k&&!F(k).timestamp&&(ce&&w.log(`[Tracing] Finishing current active span with op: ${F(k).op}`),k.setAttribute(pn,"cancelled"),k.end())}if(S.on("startNavigationSpan",k=>{if(j()!==S)return;R(),qe().setPropagationContext({traceId:Ae(),sampleRand:Math.random()});const C=q();C.setPropagationContext({traceId:Ae(),sampleRand:Math.random()}),C.setSDKProcessingMetadata({normalizedRequest:void 0}),D(S,{op:"navigation",...k})}),S.on("startPageLoadSpan",(k,C={})=>{if(j()!==S)return;R();const P=Ki(C.sentryTrace||no("sentry-trace"),C.baggage||no("baggage")),I=q();I.setPropagationContext(P),I.setSDKProcessingMetadata({normalizedRequest:_r()}),D(S,{op:"pageload",...k})}),E!=="off"&&Vc(S,{linkPreviousTrace:E,consistentTraceSampling:_}),L.location){if(v){const k=ue();(function(C,P,I){C.emit("startPageLoadSpan",P,I),q().setTransactionName(P.name),en(C)})(S,{name:L.location.pathname,startTime:k?k/1e3:void 0,attributes:{[ye]:"url",[W]:"auto.pageload.browser"}})}y&&Lr(({to:k,from:C})=>{if(C===void 0&&(O==null?void 0:O.indexOf(k))!==-1)return void(O=void 0);O=void 0;const P=si(k);(function(I,M){I.emit("startNavigationSpan",M),q().setTransactionName(M.name),en(I)})(S,{name:(P==null?void 0:P.pathname)||L.location.pathname,attributes:{[ye]:"url",[W]:"auto.navigation.browser"}}),q().setSDKProcessingMetadata({normalizedRequest:{..._r(),url:k}})})}h&&(L.document?L.document.addEventListener("visibilitychange",()=>{const k=se();if(!k)return;const C=ee(k);if(L.document.hidden&&C){const P="cancelled",{op:I,status:M}=F(C);ce&&w.log(`[Tracing] Transaction: ${P} -> since tab moved to the background, op: ${I}`),M||C.setStatus({code:U,message:P}),C.setAttribute("sentry.cancellation_reason","document.hidden"),C.end()}}):ce&&w.warn("[Tracing] Could not set up background tab detection due to lack of global document")),o&&function(k,C,P,I,M){const G=L.document;let Q;const Be=()=>{const $e="ui.action.click",Ie=en(k);if(Ie){const pe=F(Ie).op;if(["navigation","pageload"].includes(pe))return void(ce&&w.warn(`[Tracing] Did not create ${$e} span because a pageload or navigation span is in progress.`))}Q&&(Q.setAttribute(pn,"interactionInterrupted"),Q.end(),Q=void 0),M.name?Q=ms({name:M.name,op:$e,attributes:{[ye]:M.source||"url"}},{idleTimeout:C,finalTimeout:P,childSpanTimeout:I}):ce&&w.warn(`[Tracing] Did not create ${$e} transaction because _latestRouteName is missing.`)};G&&addEventListener("click",Be,{once:!1,capture:!0})}(S,c,u,l,x),n&&function(){const k=({entries:C})=>{const P=se(),I=P&&ee(P);C.forEach(M=>{if(!function(Q){return"duration"in Q}(M)||!I)return;const G=M.interactionId;if(G!=null&&!an.has(G)){if(Kn.length>10){const Q=Kn.shift();an.delete(Q)}Kn.push(G),an.set(G,I)}})};bt("event",k),bt("first-input",k)}(),Wc(S,{traceFetch:f,traceXHR:d,trackFetchStreamPerformance:p,tracePropagationTargets:S.getOptions().tracePropagationTargets,shouldCreateSpanForRequest:m,enableHTTPTimings:g,onRequestSpanStart:b})}}};function no(t){const e=L.document,n=e==null?void 0:e.querySelector(`meta[name=${t}]`);return(n==null?void 0:n.getAttribute("content"))||void 0}const Ei="_sentry_idleSpan";function en(t){return t[Ei]}function ro(t,e){ae(t,Ei,e)}class xi extends Error{constructor(e){super(e),this.name="PerformanceException"}}class Xc extends xi{constructor(n,r,s,o="unknown"){super(`Slow framerate detected: ${n.toFixed(1)} fps`);Y(this,"fps");Y(this,"threshold");Y(this,"avgFramerate");Y(this,"webviewId");this.name="SlowFramerateException",this.fps=n,this.threshold=r,this.avgFramerate=s,this.webviewId=o}}class Zc extends xi{constructor(n,r,s=null,o="unknown"){super(`Slow INP detected: ${n.toFixed(1)} ms${s?` on target: ${s}`:""}`);Y(this,"inp");Y(this,"threshold");Y(this,"target");Y(this,"webviewId");this.name="SlowINPException",this.inp=n,this.threshold=r,this.target=s,this.webviewId=o}}function Qc(t){if(window.augmentPerformance=window.augmentPerformance||{},window.augmentPerformance.initialized)return;window.augmentPerformance.initialized=!0;let e=0,n=performance.now(),r=60;const s=[];let o=0;const i=t.lowFramerateThreshold,a=t.slowInpThreshold;if(requestAnimationFrame(function c(u){const l=u-n;if(e++,l>1e3){r=1e3*e/l,e=0,n=u,s.push(r),s.length>10&&s.shift();const h=s.reduce((f,d)=>f+d,0)/s.length;if(r<i){console.error(`[Augment Performance] Slow framerate detected: ${r.toFixed(1)} fps`),console.error(`[Augment Performance] Avg framerate detected: ${h.toFixed(1)} fps`);const f=r<15,d=new Xc(r,i,h,window.location.href);It(p=>{p.setTag("performance_issue","slow_framerate"),p.setTag("fps_value",r.toFixed(1)),p.setTag("avg_fps",h.toFixed(1)),p.setTag("webview_url",window.location.href),p.setExtra("performance_data",{fps:r,avgFps:h,threshold:i,isCritical:f,framerateHistory:[...s]}),p.setLevel("warning"),pr(d)})}}requestAnimationFrame(c)}),PerformanceObserver.supportedEntryTypes.includes("event"))try{new PerformanceObserver(c=>{(u=>{const l=u.getEntries().filter(d=>"interactionId"in d&&"duration"in d&&d.startTime>0&&d.duration<1e6);if(l.length===0)return;l.sort((d,p)=>p.duration-d.duration);const h=Math.floor(.98*l.length),f=l[Math.min(h,l.length-1)].duration;if(f>a){console.error(`[Augment Performance] Slow INP detected: ${f.toFixed(1)} ms`);let d=null;const p=l[0];p&&"target"in p&&(d=p.target,console.error("[Augment Performance] Slow interaction target:",d,p));const m=new Zc(f,a,d?String(d):null,window.location.href);It(g=>{g.setTag("performance_issue","slow_inp"),g.setTag("inp_value",f.toFixed(1)),g.setTag("webview_url",window.location.href),d&&g.setTag("interaction_target",String(d)),g.setExtra("performance_data",{inp:f,threshold:a,target:d}),g.setLevel("warning"),pr(m)}),f>o&&(o=f)}})(c)}).observe({entryTypes:["event","first-input"],buffered:!0})}catch(c){console.error("[Augment Performance] Error setting up INP monitoring:",c)}else console.warn("[Augment Performance] PerformanceObserver not supported for INP monitoring");window.augmentPerformance.getFramerate=()=>r,window.augmentPerformance.getWorstINP=()=>o}const so=16,oo=200;function io(){var t;return((t=window.augmentFlags)==null?void 0:t.enablePerformanceMonitoring)??!1}let ao=!1;function eu(t){let e,n;return{c(){e=at("svg"),n=at("path"),B(n,"fill-rule","evenodd"),B(n,"clip-rule","evenodd"),B(n,"d","M11.7816 4.03157C12.0062 3.80702 12.0062 3.44295 11.7816 3.2184C11.5571 2.99385 11.193 2.99385 10.9685 3.2184L7.50005 6.68682L4.03164 3.2184C3.80708 2.99385 3.44301 2.99385 3.21846 3.2184C2.99391 3.44295 2.99391 3.80702 3.21846 4.03157L6.68688 7.49999L3.21846 10.9684C2.99391 11.193 2.99391 11.557 3.21846 11.7816C3.44301 12.0061 3.80708 12.0061 4.03164 11.7816L7.50005 8.31316L10.9685 11.7816C11.193 12.0061 11.5571 12.0061 11.7816 11.7816C12.0062 11.557 12.0062 11.193 11.7816 10.9684L8.31322 7.49999L11.7816 4.03157Z"),B(n,"fill","currentColor"),B(e,"width","15"),B(e,"height","15"),B(e,"viewBox","0 0 15 15"),B(e,"fill","none"),B(e,"xmlns","http://www.w3.org/2000/svg")},m(r,s){me(r,e,s),Ce(e,n)},p:ie,i:ie,o:ie,d(r){r&&J(e)}}}(function(){var n,r;const t=!!((r=(n=window.augmentFlags)==null?void 0:n.sentry)!=null&&r.enabled);var e;(e={enabled:io(),lowFramerateThreshold:so,slowInpThreshold:oo}).enabled&&Qc({lowFramerateThreshold:e.lowFramerateThreshold||so,slowInpThreshold:e.slowInpThreshold||oo}),io()&&!t&&console.warn("[Augment Performance] Performance monitoring enabled but Sentry is not initialized. Performance issues will not be reported to Sentry.")})(),function(){var e,n;if(!((n=(e=window.augmentFlags)==null?void 0:e.sentry)!=null&&n.enabled))return;const t=window.augmentFlags.sentry;if(t)if(ao)console.warn("Sentry is already initialized, duplicate initialization attempt");else try{(function(r){const s={...r};ti(s,"svelte"),Hc(s)})({dsn:t.dsn,release:t.release,environment:t.environment,tracesSampleRate:t.tracesSampleRate||0,replaysSessionSampleRate:t.replaysSessionSampleRate||0,replaysOnErrorSampleRate:t.replaysOnErrorSampleRate||0,sampleRate:t.errorSampleRate||0,sendDefaultPii:t.sendDefaultPii!==void 0&&t.sendDefaultPii,integrations:(()=>{const r=[];return t.tracesSampleRate&&t.tracesSampleRate>0&&r.push(Kc()),r})(),beforeSend:r=>r}),t.tags&&Object.entries(t.tags).forEach(([r,s])=>{(function(o,i){qe().setTag(o,i)})(r,String(s))}),ao=!0}catch(r){console.error("Failed to initialize Sentry:",r)}else console.warn("Sentry configuration not found in window.augmentDeps")}();class Pu extends Me{constructor(e){super(),je(this,e,null,eu,Fe,{})}}function tu(t){let e,n,r=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},t[0]],s={};for(let o=0;o<r.length;o+=1)s=Z(s,r[o]);return{c(){e=at("svg"),n=new vn(!0),this.h()},l(o){e=yn(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=bn(e);n=Sn(i,!0),i.forEach(J),this.h()},h(){n.a=null,Pe(e,s)},m(o,i){wn(o,e,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M304 128a80 80 0 1 0-160 0 80 80 0 1 0 160 0m-208 0a128 128 0 1 1 256 0 128 128 0 1 1-256 0M49.3 464h349.5c-8.9-63.3-63.3-112-129-112h-91.4c-65.7 0-120.1 48.7-129 112zM0 482.3C0 383.8 79.8 304 178.3 304h91.4c98.5 0 178.3 79.8 178.3 178.3 0 16.4-13.3 29.7-29.7 29.7H29.7C13.3 512 0 498.7 0 482.3"/>',e)},p(o,[i]){Pe(e,s=mt(r,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&i&&o[0]]))},i:ie,o:ie,d(o){o&&J(e)}}}function nu(t,e,n){return t.$$set=r=>{n(0,e=Z(Z({},e),we(r)))},[e=we(e)]}class Au extends Me{constructor(e){super(),je(this,e,nu,tu,Fe,{})}}class _n{constructor(e){this._opts=e}get color(){return this._opts.color}get size(){return this._opts.size??1}get variant(){return this._opts.variant}}Y(_n,"CONTEXT_KEY","augment-badge");const ru=t=>({}),co=t=>({}),su=t=>({}),uo=t=>({}),ou=t=>({}),lo=t=>({});function po(t){let e,n;const r=t[7].leftButtons,s=Dt(r,t,t[16],uo);return{c(){e=Ee("div"),s&&s.c(),B(e,"class","c-badge__left-buttons svelte-1ulw6l3")},m(o,i){me(o,e,i),s&&s.m(e,null),n=!0},p(o,i){s&&s.p&&(!n||65536&i)&&Lt(s,r,o,o[16],n?Mt(r,o[16],i,su):Nt(o[16]),uo)},i(o){n||(K(s,o),n=!0)},o(o){re(s,o),n=!1},d(o){o&&J(e),s&&s.d(o)}}}function fo(t){let e,n;return e=new Ai({props:{size:t[5],weight:"medium",$$slots:{default:[iu]},$$scope:{ctx:t}}}),{c(){_o(e.$$.fragment)},m(r,s){vo(e,r,s),n=!0},p(r,s){const o={};65536&s&&(o.$$scope={dirty:s,ctx:r}),e.$set(o)},i(r){n||(K(e.$$.fragment,r),n=!0)},o(r){re(e.$$.fragment,r),n=!1},d(r){yo(e,r)}}}function iu(t){let e,n;const r=t[7].default,s=Dt(r,t,t[16],null);return{c(){e=Ee("div"),s&&s.c(),B(e,"class","c-badge-body svelte-1ulw6l3")},m(o,i){me(o,e,i),s&&s.m(e,null),n=!0},p(o,i){s&&s.p&&(!n||65536&i)&&Lt(s,r,o,o[16],n?Mt(r,o[16],i,null):Nt(o[16]),null)},i(o){n||(K(s,o),n=!0)},o(o){re(s,o),n=!1},d(o){o&&J(e),s&&s.d(o)}}}function ho(t){let e,n;const r=t[7].rightButtons,s=Dt(r,t,t[16],co);return{c(){e=Ee("div"),s&&s.c(),B(e,"class","c-badge__right-buttons svelte-1ulw6l3")},m(o,i){me(o,e,i),s&&s.m(e,null),n=!0},p(o,i){s&&s.p&&(!n||65536&i)&&Lt(s,r,o,o[16],n?Mt(r,o[16],i,ru):Nt(o[16]),co)},i(o){n||(K(s,o),n=!0)},o(o){re(s,o),n=!1},d(o){o&&J(e),s&&s.d(o)}}}function au(t){let e,n,r,s,o,i,a,c;const u=t[7].chaser,l=Dt(u,t,t[16],lo);let h=t[6].leftButtons&&po(t),f=t[6].default&&fo(t),d=t[6].rightButtons&&ho(t),p=[Mr(t[0]),jr(t[3]),{class:o=`c-badge c-badge--${t[0]} c-badge--${t[1]} c-badge--size-${t[2]}`},{role:"button"},{tabindex:"0"}],m={};for(let g=0;g<p.length;g+=1)m=Z(m,p[g]);return{c(){e=Ee("div"),l&&l.c(),n=St(),h&&h.c(),r=St(),f&&f.c(),s=St(),d&&d.c(),Fr(e,m),X(e,"c-badge--highContrast",t[4]),X(e,"svelte-1ulw6l3",!0)},m(g,v){me(g,e,v),l&&l.m(e,null),Ce(e,n),h&&h.m(e,null),Ce(e,r),f&&f.m(e,null),Ce(e,s),d&&d.m(e,null),i=!0,a||(c=[fe(e,"click",t[8]),fe(e,"keydown",t[9]),fe(e,"keyup",t[10]),fe(e,"mousedown",t[11]),fe(e,"mouseover",t[12]),fe(e,"focus",t[13]),fe(e,"mouseleave",t[14]),fe(e,"blur",t[15])],a=!0)},p(g,[v]){l&&l.p&&(!i||65536&v)&&Lt(l,u,g,g[16],i?Mt(u,g[16],v,ou):Nt(g[16]),lo),g[6].leftButtons?h?(h.p(g,v),64&v&&K(h,1)):(h=po(g),h.c(),K(h,1),h.m(e,r)):h&&(On(),re(h,1,1,()=>{h=null}),Rn()),g[6].default?f?(f.p(g,v),64&v&&K(f,1)):(f=fo(g),f.c(),K(f,1),f.m(e,s)):f&&(On(),re(f,1,1,()=>{f=null}),Rn()),g[6].rightButtons?d?(d.p(g,v),64&v&&K(d,1)):(d=ho(g),d.c(),K(d,1),d.m(e,null)):d&&(On(),re(d,1,1,()=>{d=null}),Rn()),Fr(e,m=mt(p,[1&v&&Mr(g[0]),8&v&&jr(g[3]),(!i||7&v&&o!==(o=`c-badge c-badge--${g[0]} c-badge--${g[1]} c-badge--size-${g[2]}`))&&{class:o},{role:"button"},{tabindex:"0"}])),X(e,"c-badge--highContrast",g[4]),X(e,"svelte-1ulw6l3",!0)},i(g){i||(K(l,g),K(h),K(f),K(d),i=!0)},o(g){re(l,g),re(h),re(f),re(d),i=!1},d(g){g&&J(e),l&&l.d(g),h&&h.d(),f&&f.d(),d&&d.d(),a=!1,go(c)}}}function cu(t,e,n){let{$$slots:r={},$$scope:s}=e;const o=Ci(r);let{color:i="accent"}=e,{variant:a="soft"}=e,{size:c=1}=e,{radius:u="medium"}=e,{highContrast:l=!1}=e;const h=c===3?2:1,f=new _n({color:i,size:c,variant:a});return Pi(_n.CONTEXT_KEY,f),t.$$set=d=>{"color"in d&&n(0,i=d.color),"variant"in d&&n(1,a=d.variant),"size"in d&&n(2,c=d.size),"radius"in d&&n(3,u=d.radius),"highContrast"in d&&n(4,l=d.highContrast),"$$scope"in d&&n(16,s=d.$$scope)},[i,a,c,u,l,h,o,r,function(d){H.call(this,t,d)},function(d){H.call(this,t,d)},function(d){H.call(this,t,d)},function(d){H.call(this,t,d)},function(d){H.call(this,t,d)},function(d){H.call(this,t,d)},function(d){H.call(this,t,d)},function(d){H.call(this,t,d)},s]}class uu extends Me{constructor(e){super(),je(this,e,cu,au,Fe,{color:0,variant:1,size:2,radius:3,highContrast:4})}}function du(t){let e;const n=t[7].default,r=Dt(n,t,t[17],null);return{c(){r&&r.c()},m(s,o){r&&r.m(s,o),e=!0},p(s,o){r&&r.p&&(!e||131072&o)&&Lt(r,n,s,s[17],e?Mt(n,s[17],o,null):Nt(s[17]),null)},i(s){e||(K(r,s),e=!0)},o(s){re(r,s),e=!1},d(s){r&&r.d(s)}}}function lu(t){let e,n,r;const s=[{size:t[6]},{variant:pu},{color:t[0]},{highContrast:t[1]},{disabled:t[2]},{class:`c-badge-icon-btn__base-btn ${t[4]}`},t[3]];let o={$$slots:{default:[du]},$$scope:{ctx:t}};for(let i=0;i<s.length;i+=1)o=Z(o,s[i]);return n=new Ni({props:o}),n.$on("click",t[8]),n.$on("keyup",t[9]),n.$on("keydown",t[10]),n.$on("mousedown",t[11]),n.$on("mouseover",t[12]),n.$on("focus",t[13]),n.$on("mouseleave",t[14]),n.$on("blur",t[15]),n.$on("contextmenu",t[16]),{c(){e=Ee("div"),_o(n.$$.fragment),B(e,"class",Oi(`c-badge-icon-btn c-badge-icon-btn--${t[5].variant} c-badge-icon-btn--size-${t[6]}`)+" svelte-1im94um")},m(i,a){me(i,e,a),vo(n,e,null),r=!0},p(i,[a]){const c=95&a?mt(s,[64&a&&{size:i[6]},0,1&a&&{color:i[0]},2&a&&{highContrast:i[1]},4&a&&{disabled:i[2]},16&a&&{class:`c-badge-icon-btn__base-btn ${i[4]}`},8&a&&Ri(i[3])]):{};131072&a&&(c.$$scope={dirty:a,ctx:i}),n.$set(c)},i(i){r||(K(n.$$.fragment,i),r=!0)},o(i){re(n.$$.fragment,i),r=!1},d(i){i&&J(e),yo(n)}}}let pu="ghost";function fu(t,e){return typeof t=="string"&&["accent","neutral","error","success","warning","info"].includes(t)?t:e}function hu(t,e,n){let r,s;const o=["color","highContrast","disabled"];let i=qr(e,o),{$$slots:a={},$$scope:c}=e;const u=Di(_n.CONTEXT_KEY);let{color:l=fu(u.color,"neutral")}=e,{highContrast:h=!1}=e,{disabled:f=!1}=e,d=u.size;return t.$$set=p=>{e=Z(Z({},e),we(p)),n(18,i=qr(e,o)),"color"in p&&n(0,l=p.color),"highContrast"in p&&n(1,h=p.highContrast),"disabled"in p&&n(2,f=p.disabled),"$$scope"in p&&n(17,c=p.$$scope)},t.$$.update=()=>{n(4,{class:r,...s}=i,r,(n(3,s),n(18,i)))},[l,h,f,s,r,u,d,a,function(p){H.call(this,t,p)},function(p){H.call(this,t,p)},function(p){H.call(this,t,p)},function(p){H.call(this,t,p)},function(p){H.call(this,t,p)},function(p){H.call(this,t,p)},function(p){H.call(this,t,p)},function(p){H.call(this,t,p)},function(p){H.call(this,t,p)},c]}const Ou={Root:uu,IconButton:class extends Me{constructor(t){super(),je(this,t,hu,lu,Fe,{color:0,highContrast:1,disabled:2})}}};function mo(t){let e,n,r,s,o,i=(t[5]||"")+"",a=(t[4]||"")+"";return{c(){e=Ee("span"),n=Br(i),r=St(),s=Ee("span"),o=Br(a),B(e,"class","c-toggle-text c-toggle-text--off svelte-xr5g0k"),X(e,"visible",!t[0]&&t[5]),B(s,"class","c-toggle-text c-toggle-text--on svelte-xr5g0k"),X(s,"visible",t[0]&&t[4])},m(c,u){me(c,e,u),Ce(e,n),me(c,r,u),me(c,s,u),Ce(s,o)},p(c,u){32&u&&i!==(i=(c[5]||"")+"")&&zr(n,i),33&u&&X(e,"visible",!c[0]&&c[5]),16&u&&a!==(a=(c[4]||"")+"")&&zr(o,a),17&u&&X(s,"visible",c[0]&&c[4])},d(c){c&&(J(e),J(r),J(s))}}}function mu(t){let e,n,r,s,o,i,a=t[6]&&mo(t);return{c(){e=Ee("label"),a&&a.c(),n=St(),r=Ee("input"),B(r,"type","checkbox"),B(r,"class","c-toggle-input svelte-xr5g0k"),r.disabled=t[1],B(r,"aria-label",t[3]),B(r,"role","switch"),X(r,"disabled",t[1]),B(e,"class",s="c-toggle-track c-toggle-track-size--"+t[2]+" svelte-xr5g0k"),X(e,"checked",t[0]),X(e,"disabled",t[1]),X(e,"has-text",t[6])},m(c,u){me(c,e,u),a&&a.m(e,null),Ce(e,n),Ce(e,r),r.checked=t[0],o||(i=[fe(r,"change",t[9]),fe(r,"keydown",t[7]),fe(e,"change",t[8])],o=!0)},p(c,[u]){c[6]?a?a.p(c,u):(a=mo(c),a.c(),a.m(e,n)):a&&(a.d(1),a=null),2&u&&(r.disabled=c[1]),8&u&&B(r,"aria-label",c[3]),1&u&&(r.checked=c[0]),2&u&&X(r,"disabled",c[1]),4&u&&s!==(s="c-toggle-track c-toggle-track-size--"+c[2]+" svelte-xr5g0k")&&B(e,"class",s),5&u&&X(e,"checked",c[0]),6&u&&X(e,"disabled",c[1]),68&u&&X(e,"has-text",c[6])},i:ie,o:ie,d(c){c&&J(e),a&&a.d(),o=!1,go(i)}}}function gu(t,e,n){let r,{checked:s=!1}=e,{disabled:o=!1}=e,{size:i=2}=e,{ariaLabel:a}=e,{onText:c}=e,{offText:u}=e;return t.$$set=l=>{"checked"in l&&n(0,s=l.checked),"disabled"in l&&n(1,o=l.disabled),"size"in l&&n(2,i=l.size),"ariaLabel"in l&&n(3,a=l.ariaLabel),"onText"in l&&n(4,c=l.onText),"offText"in l&&n(5,u=l.offText)},t.$$.update=()=>{48&t.$$.dirty&&n(6,r=c||u)},[s,o,i,a,c,u,r,function(l){o||l.key!=="Enter"&&l.key!==" "||(l.preventDefault(),n(0,s=!s))},function(l){H.call(this,t,l)},function(){s=this.checked,n(0,s)}]}class Ru extends Me{constructor(e){super(),je(this,e,gu,mu,Fe,{checked:0,disabled:1,size:2,ariaLabel:3,onText:4,offText:5})}}const er={enabled:!1,volume:.5},Du={enabled:!0},_u=""+new URL("agent-complete-DO0gyADk.mp3",import.meta.url).href;var Ti=(t=>(t.AGENT_COMPLETE="agent-complete",t))(Ti||{});const vu={"agent-complete":_u},He=class He{constructor(){Y(this,"audioCache",new Map)}static getInstance(){return He._instance||(He._instance=new He),He._instance}retrieveAudioElement(e,n){let r=this.audioCache.get(e);return r?r.volume=n.volume:(r=new Audio,r.src=vu[e],r.volume=n.volume,r.preload="auto",r._isUnlocked=!1,this.audioCache.set(e,r)),r}async playSound(e,n){if(n.enabled)try{const r=this.retrieveAudioElement(e,n);r.currentTime=0,await r.play()}catch(r){if(r instanceof DOMException&&r.name==="NotAllowedError")return void console.error("Audio blocked by browser policy. Sound will work after user interaction.");console.error("Failed to play sound:",r)}}async unlockSoundForConfig(e){if(!e.enabled)return;const n=this.retrieveAudioElement("agent-complete",e);if(!n._isUnlocked)try{await this.playSound("agent-complete",{enabled:!0,volume:0}),n._isUnlocked=!0}catch(r){console.warn("Failed to unlock sound:",r)}}disposeSounds(){this.audioCache.forEach(e=>{e.pause(),e.src="",e._isUnlocked=!1}),this.audioCache.clear()}};Y(He,"_instance");let wr=He;const tr=wr.getInstance();class yu{constructor(e){Y(this,"_soundSettings",Li(er));Y(this,"_isLoaded",!1);Y(this,"dispose",()=>{tr.disposeSounds()});this._msgBroker=e,this.initialize()}async refreshSettings(){try{const e=await this._msgBroker.sendToSidecar({type:Ln.getSoundSettings});e.data&&this._soundSettings.set(e.data)}catch(e){console.warn("Failed to refresh sound settings:",e)}}async unlockSound(){Dn(this._soundSettings).enabled&&tr.unlockSoundForConfig(Dn(this._soundSettings))}async playAgentComplete(){const e=Dn(this._soundSettings);await tr.playSound(Ti.AGENT_COMPLETE,e)}get getCurrentSettings(){return this._soundSettings}async initialize(){if(!this._isLoaded)try{const e=await this._msgBroker.sendToSidecar({type:Ln.getSoundSettings});e.data&&this._soundSettings.set(e.data),this._isLoaded=!0}catch(e){console.warn("Failed to load sound settings, using defaults:",e),this._soundSettings.set(er),this._isLoaded=!0}}async updateSettings(e){try{await this._msgBroker.sendToSidecar({type:Ln.updateSoundSettings,data:e}),this._soundSettings.update(n=>({...n,...e}))}catch(n){throw console.error("Failed to update sound settings:",n),n}}async setEnabled(e){await this.updateSettings({enabled:e})}async setVolume(e){const n=Math.max(0,Math.min(1,e));await this.updateSettings({volume:n})}async resetToDefaults(){await this.updateSettings(er)}updateEnabled(e){this.setEnabled(e).catch(n=>{console.error("Failed to update enabled setting:",n)})}updateVolume(e){this.setVolume(e).catch(n=>{console.error("Failed to update volume setting:",n)})}}Y(yu,"key","soundModel");function bu(t){let e,n,r=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},t[0]],s={};for(let o=0;o<r.length;o+=1)s=Z(s,r[o]);return{c(){e=at("svg"),n=new vn(!0),this.h()},l(o){e=yn(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=bn(e);n=Sn(i,!0),i.forEach(J),this.h()},h(){n.a=null,Pe(e,s)},m(o,i){wn(o,e,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M248 72c0-13.3-10.7-24-24-24s-24 10.7-24 24v160H40c-13.3 0-24 10.7-24 24s10.7 24 24 24h160v160c0 13.3 10.7 24 24 24s24-10.7 24-24V280h160c13.3 0 24-10.7 24-24s-10.7-24-24-24H248z"/>',e)},p(o,[i]){Pe(e,s=mt(r,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&i&&o[0]]))},i:ie,o:ie,d(o){o&&J(e)}}}function Su(t,e,n){return t.$$set=r=>{n(0,e=Z(Z({},e),we(r)))},[e=we(e)]}class Lu extends Me{constructor(e){super(),je(this,e,Su,bu,Fe,{})}}function wu(t){let e,n,r=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},t[0]],s={};for(let o=0;o<r.length;o+=1)s=Z(s,r[o]);return{c(){e=at("svg"),n=new vn(!0),this.h()},l(o){e=yn(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=bn(e);n=Sn(i,!0),i.forEach(J),this.h()},h(){n.a=null,Pe(e,s)},m(o,i){wn(o,e,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="M280 24c0-13.3-10.7-24-24-24s-24 10.7-24 24v270.1l-95-95c-9.4-9.4-24.6-9.4-33.9 0s-9.4 24.6 0 33.9L239 369c9.4 9.4 24.6 9.4 33.9 0L409 233c9.4-9.4 9.4-24.6 0-33.9s-24.6-9.4-33.9 0l-95 95zM128.8 304H64c-35.3 0-64 28.7-64 64v80c0 35.3 28.7 64 64 64h384c35.3 0 64-28.7 64-64v-80c0-35.3-28.7-64-64-64h-64.8l-48 48H448c8.8 0 16 7.2 16 16v80c0 8.8-7.2 16-16 16H64c-8.8 0-16-7.2-16-16v-80c0-8.8 7.2-16 16-16h112.8zM432 408a24 24 0 1 0-48 0 24 24 0 1 0 48 0"/>',e)},p(o,[i]){Pe(e,s=mt(r,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 512 512"},1&i&&o[0]]))},i:ie,o:ie,d(o){o&&J(e)}}}function Eu(t,e,n){return t.$$set=r=>{n(0,e=Z(Z({},e),we(r)))},[e=we(e)]}class Nu extends Me{constructor(e){super(),je(this,e,Eu,wu,Fe,{})}}function xu(t){let e,n,r=[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},t[0]],s={};for(let o=0;o<r.length;o+=1)s=Z(s,r[o]);return{c(){e=at("svg"),n=new vn(!0),this.h()},l(o){e=yn(o,"svg",{xmlns:!0,"data-ds-icon":!0,viewBox:!0});var i=bn(e);n=Sn(i,!0),i.forEach(J),this.h()},h(){n.a=null,Pe(e,s)},m(o,i){wn(o,e,i),n.m('<!--! Font Awesome Pro 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license (Commercial License) Copyright 2024 Fonticons, Inc.--><path d="m170.5 51.6-19 28.4h145l-19-28.4c-1.5-2.2-4-3.6-6.7-3.6h-93.7c-2.7 0-5.2 1.3-6.7 3.6zm147-26.6 36.7 55H424c13.3 0 24 10.7 24 24s-10.7 24-24 24h-8v304c0 44.2-35.8 80-80 80H112c-44.2 0-80-35.8-80-80V128h-8c-13.3 0-24-10.7-24-24s10.7-24 24-24h69.8l36.7-55.1C140.9 9.4 158.4 0 177.1 0h93.7c18.7 0 36.2 9.4 46.6 24.9zM80 128v304c0 17.7 14.3 32 32 32h224c17.7 0 32-14.3 32-32V128zm80 64v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16m80 0v208c0 8.8-7.2 16-16 16s-16-7.2-16-16V192c0-8.8 7.2-16 16-16s16 7.2 16 16"/>',e)},p(o,[i]){Pe(e,s=mt(r,[{xmlns:"http://www.w3.org/2000/svg"},{"data-ds-icon":"fa"},{viewBox:"0 0 448 512"},1&i&&o[0]]))},i:ie,o:ie,d(o){o&&J(e)}}}function Tu(t,e,n){return t.$$set=r=>{n(0,e=Z(Z({},e),we(r)))},[e=we(e)]}class Mu extends Me{constructor(e){super(),je(this,e,Tu,xu,Fe,{})}}export{Ou as B,Pu as C,er as D,Lu as P,Ti as S,Mu as T,Au as U,Du as a,Ru as b,Nu as c,yu as d,uu as e,tr as s};
